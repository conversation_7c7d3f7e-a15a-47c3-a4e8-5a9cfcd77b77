const express = require("express");
const cookieParser = require("cookie-parser");
const cors = require("cors");

const port = 5000;
const app = express();
app.use(express.json());
app.use(cors({ origin: "http://localhost:3000", credentials: true }));

let TODOS = [
 { id: 1, title: "Learn React"},
 { id: 2, title: "Learn Node"},
 { id: 3, title: "Learn Express"},
];

app.post("/api/auth/login", (req, res) => {
 const { email, password } = req.body;
 // Add your authentication logic here
 if (email === "<EMAIL>" && password === "123123123") {
  const accessToken =
   "eyJhbGciOiJIUzI1NiJ9.************************************************************************.EFtVTx0xYaq-u0ZJerF_FA0p8_2DZ0LNhK-Ha71SZ14";
  const refreshToken =
   "c13fc6f1-ee52-43f2-b7ef-66c318366eac0f430a6b-ab36-4353-b87e-a2f607d56e63";

  res.cookie("JWT_test", accessToken, { httpOnly: true });
  res.cookie("JWT_REFRESH_test", refreshToken, { httpOnly: true });

  res.status(200).json({
   status: "OK",
   success: true,
   data: {
    accessToken: accessToken,
    user: {
     id: "44e22705-e52f-4c0a-b061-c69aaf38918c",
     email: "<EMAIL>",
     username: "<EMAIL>",
     lastLogin: "2025-03-04T02:25:05.138567029+02:00",
     createdAt: "2025-02-27T17:14:03.4706Z",
     roles: [
      {
       id: 1,
       name: "ROLE_USER",
       permissions: [
        {
         permissionName: "CREATE_ORG",
         description: "Create an organization",
         category: "USER",
        },
       ],
      },
     ],
    },
    refreshToken: refreshToken,
   },
   error: null,
   timestamp: new Date().toISOString(),
  });
 } else {
  res.status(401).send("Invalid credentials");
 }
});

app.get("/api/todos", (req, res) => {
 res.status(200).json(TODOS);
});

app.post("/api/todos", (req, res) => {
 const todo = req.body;
 TODOS.push(todo);
 res.status(201).json(todo);
});

app.listen(port, () => {
 console.log(`Server is running on port ${port}`);
});
