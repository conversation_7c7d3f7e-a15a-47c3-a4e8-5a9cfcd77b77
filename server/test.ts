let elements = [
  {
    "id": "element-1",
    "type": "short_text",
    "question": "What is your name?",
    "required": true,
    "options": []
  },
  {
    "id": "element-2",
    "type": "long_text",
    "question": "Please provide your feedback",
    "required": false,
    "options": []
  },
  {
    "id": "element-3",
    "type": "multiple_choice",
    "question": "How would you rate our service?",
    "required": true,
    "options": [
      "Excellent",
      "Good",
      "Average",
      "Poor",
      "Very Poor"
    ]
  },
  {
    "id": "element-4",
    "type": "checkbox",
    "question": "Which features do you use?",
    "required": false,
    "options": [
      "Feature 1",
      "Feature 2",
      "Feature 3",
      "Feature 4"
    ]
  },
  {
    "id": "element-5",
    "type": "date",
    "question": "When did you start using our service?",
    "required": false,
    "options": []
  }
]


export enum ElementType {
  SHORT_TEXT = "short_text",
  LONG_TEXT = "long_text",
  MULTIPLE_CHOICE = "multiple_choice",
  CHECKBOX = "checkbox",
  DROPDOWN = "dropdown",
  DATE = "date",
  TIME = "time",
  FILE_UPLOAD = "file_upload",
}

export interface BaseFormElement {
  id: string;
  type: ElementType;
  question: string;
  required: boolean;
}

export interface TextFormElement extends BaseFormElement {
  type: ElementType.SHORT_TEXT | ElementType.LONG_TEXT;
  options: never[];
}

export interface ChoiceFormElement extends BaseFormElement {
  type: ElementType.MULTIPLE_CHOICE | ElementType.CHECKBOX | ElementType.DROPDOWN;
  options: string[];
}

export interface DateFormElement extends BaseFormElement {
  type: ElementType.DATE;
  options: never[];
}

export interface TimeFormElement extends BaseFormElement {
  type: ElementType.TIME;
  options: never[];
}

export interface FileFormElement extends BaseFormElement {
  type: ElementType.FILE_UPLOAD;
  options: never[];
}
