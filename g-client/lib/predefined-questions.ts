// Popular coding questions from big tech companies
export interface PredefinedQuestion {
  id: string;
  title: string;
  text: string;
  language: string;
  starterCode: string;
  solutionCode: string;
  testCases: Array<{
    input: string;
    expected_output: string;
    weight: number;
  }>;
  evaluationCriteria: {
    timeComplexity: string;
    spaceComplexity: string;
    constraints: string[];
  };
  gradingRules: {
    testCaseWeight: number;
    codeQualityWeight: number;
    efficiencyWeight: number;
    partialCredit: boolean;
  };
  metadata: {
    difficulty: "EASY" | "MEDIUM" | "HARD";
    estimatedDuration: number;
    tags: string[];
    companies: string[];
    topic: string;
  };
}

export const QUESTION_TOPICS = [
  "Arrays & Strings",
  "Linked Lists",
  "Trees & Graphs",
  "Dynamic Programming",
  "Sorting & Searching",
  "Hash Tables",
  "Stack & Queue",
  "Recursion & Backtracking",
  "Two Pointers",
  "Sliding Window",
  "Binary Search",
  "Greedy Algorithms",
  "Bit Manipulation",
  "Math & Geometry"
];

export const PREDEFINED_QUESTIONS: PredefinedQuestion[] = [
  {
    id: "two-sum",
    title: "Two Sum",
    text: "Given an array of integers nums and an integer target, return indices of the two numbers such that they add up to target.\n\nYou may assume that each input would have exactly one solution, and you may not use the same element twice.\n\nYou can return the answer in any order.\n\n**Example 1:**\nInput: nums = [2,7,11,15], target = 9\nOutput: [0,1]\nExplanation: Because nums[0] + nums[1] == 9, we return [0, 1].\n\n**Example 2:**\nInput: nums = [3,2,4], target = 6\nOutput: [1,2]\n\n**Example 3:**\nInput: nums = [3,3], target = 6\nOutput: [0,1]",
    language: "python",
    starterCode: "def two_sum(nums, target):\n    # Your code here\n    pass",
    solutionCode: "def two_sum(nums, target):\n    num_map = {}\n    for i, num in enumerate(nums):\n        complement = target - num\n        if complement in num_map:\n            return [num_map[complement], i]\n        num_map[num] = i\n    return []",
    testCases: [
      { input: "[2,7,11,15], 9", expected_output: "[0,1]", weight: 0.3 },
      { input: "[3,2,4], 6", expected_output: "[1,2]", weight: 0.3 },
      { input: "[3,3], 6", expected_output: "[0,1]", weight: 0.4 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(n)",
      constraints: ["Each input has exactly one solution", "Cannot use same element twice"]
    },
    gradingRules: {
      testCaseWeight: 0.7,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.1,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 15,
      tags: ["arrays", "hash-table"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook", "Apple"],
      topic: "Arrays & Strings"
    }
  },
  {
    id: "reverse-string",
    title: "Reverse a String",
    text: "Write a function that reverses a string. The input string is given as an array of characters s.\n\nYou must do this by modifying the input array in-place with O(1) extra memory.\n\n**Example 1:**\nInput: s = ['h','e','l','l','o']\nOutput: ['o','l','l','e','h']\n\n**Example 2:**\nInput: s = ['H','a','n','n','a','h']\nOutput: ['h','a','n','n','a','H']",
    language: "python",
    starterCode: "def reverse_string(s):\n    # Your code here\n    pass",
    solutionCode: "def reverse_string(s):\n    left, right = 0, len(s) - 1\n    while left < right:\n        s[left], s[right] = s[right], s[left]\n        left += 1\n        right -= 1",
    testCases: [
      { input: "['h','e','l','l','o']", expected_output: "['o','l','l','e','h']", weight: 0.4 },
      { input: "['H','a','n','n','a','h']", expected_output: "['h','a','n','n','a','H']", weight: 0.3 },
      { input: "['a']", expected_output: "['a']", weight: 0.3 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(1)",
      constraints: ["Must modify array in-place", "O(1) extra memory"]
    },
    gradingRules: {
      testCaseWeight: 0.7,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.1,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 10,
      tags: ["strings", "two-pointers"],
      companies: ["Google", "Amazon", "Microsoft"],
      topic: "Arrays & Strings"
    }
  },
  {
    id: "valid-parentheses",
    title: "Valid Parentheses",
    text: "Given a string s containing just the characters '(', ')', '{', '}', '[' and ']', determine if the input string is valid.\n\nAn input string is valid if:\n1. Open brackets must be closed by the same type of brackets.\n2. Open brackets must be closed in the correct order.\n3. Every close bracket has a corresponding open bracket of the same type.\n\n**Example 1:**\nInput: s = '()'\nOutput: true\n\n**Example 2:**\nInput: s = '()[]{}'\nOutput: true\n\n**Example 3:**\nInput: s = '(]'\nOutput: false",
    language: "python",
    starterCode: "def is_valid(s):\n    # Your code here\n    pass",
    solutionCode: "def is_valid(s):\n    stack = []\n    mapping = {')': '(', '}': '{', ']': '['}\n    \n    for char in s:\n        if char in mapping:\n            if not stack or stack.pop() != mapping[char]:\n                return False\n        else:\n            stack.append(char)\n    \n    return not stack",
    testCases: [
      { input: "'()'", expected_output: "true", weight: 0.2 },
      { input: "'()[]{}'", expected_output: "true", weight: 0.3 },
      { input: "'(]'", expected_output: "false", weight: 0.2 },
      { input: "'([)]'", expected_output: "false", weight: 0.3 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(n)",
      constraints: ["Only contains parentheses characters", "Must handle all bracket types"]
    },
    gradingRules: {
      testCaseWeight: 0.8,
      codeQualityWeight: 0.1,
      efficiencyWeight: 0.1,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 15,
      tags: ["stack", "string"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook"],
      topic: "Stack & Queue"
    }
  },
  {
    id: "maximum-subarray",
    title: "Maximum Subarray",
    text: "Given an integer array nums, find the contiguous subarray (containing at least one number) which has the largest sum and return its sum.\n\nA subarray is a contiguous part of an array.\n\n**Example 1:**\nInput: nums = [-2,1,-3,4,-1,2,1,-5,4]\nOutput: 6\nExplanation: [4,-1,2,1] has the largest sum = 6.\n\n**Example 2:**\nInput: nums = [1]\nOutput: 1\n\n**Example 3:**\nInput: nums = [5,4,-1,7,8]\nOutput: 23",
    language: "python",
    starterCode: "def max_subarray(nums):\n    # Your code here\n    pass",
    solutionCode: "def max_subarray(nums):\n    max_sum = nums[0]\n    current_sum = nums[0]\n    \n    for i in range(1, len(nums)):\n        current_sum = max(nums[i], current_sum + nums[i])\n        max_sum = max(max_sum, current_sum)\n    \n    return max_sum",
    testCases: [
      { input: "[-2,1,-3,4,-1,2,1,-5,4]", expected_output: "6", weight: 0.4 },
      { input: "[1]", expected_output: "1", weight: 0.2 },
      { input: "[5,4,-1,7,8]", expected_output: "23", weight: 0.4 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(1)",
      constraints: ["Array contains at least one number", "Must find contiguous subarray"]
    },
    gradingRules: {
      testCaseWeight: 0.6,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.2,
      partialCredit: true
    },
    metadata: {
      difficulty: "MEDIUM",
      estimatedDuration: 20,
      tags: ["array", "dynamic-programming", "divide-and-conquer"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook", "Apple"],
      topic: "Dynamic Programming"
    }
  },
  {
    id: "binary-tree-inorder",
    title: "Binary Tree Inorder Traversal",
    text: "Given the root of a binary tree, return the inorder traversal of its nodes' values.\n\n**Example 1:**\nInput: root = [1,null,2,3]\nOutput: [1,3,2]\n\n**Example 2:**\nInput: root = []\nOutput: []\n\n**Example 3:**\nInput: root = [1]\nOutput: [1]",
    language: "python",
    starterCode: "# Definition for a binary tree node.\nclass TreeNode:\n    def __init__(self, val=0, left=None, right=None):\n        self.val = val\n        self.left = left\n        self.right = right\n\ndef inorder_traversal(root):\n    # Your code here\n    pass",
    solutionCode: "def inorder_traversal(root):\n    result = []\n    \n    def inorder(node):\n        if node:\n            inorder(node.left)\n            result.append(node.val)\n            inorder(node.right)\n    \n    inorder(root)\n    return result",
    testCases: [
      { input: "[1,null,2,3]", expected_output: "[1,3,2]", weight: 0.4 },
      { input: "[]", expected_output: "[]", weight: 0.2 },
      { input: "[1]", expected_output: "[1]", weight: 0.4 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(n)",
      constraints: ["Must traverse in inorder", "Handle empty tree"]
    },
    gradingRules: {
      testCaseWeight: 0.7,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.1,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 15,
      tags: ["tree", "depth-first-search", "stack", "binary-tree"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook"],
      topic: "Trees & Graphs"
    }
  },
  {
    id: "merge-two-sorted-lists",
    title: "Merge Two Sorted Lists",
    text: "You are given the heads of two sorted linked lists list1 and list2.\n\nMerge the two lists in a one sorted list. The list should be made by splicing together the nodes of the first two lists.\n\nReturn the head of the merged linked list.\n\n**Example 1:**\nInput: list1 = [1,2,4], list2 = [1,3,4]\nOutput: [1,1,2,3,4,4]\n\n**Example 2:**\nInput: list1 = [], list2 = []\nOutput: []\n\n**Example 3:**\nInput: list1 = [], list2 = [0]\nOutput: [0]",
    language: "python",
    starterCode: "# Definition for singly-linked list.\nclass ListNode:\n    def __init__(self, val=0, next=None):\n        self.val = val\n        self.next = next\n\ndef merge_two_lists(list1, list2):\n    # Your code here\n    pass",
    solutionCode: "def merge_two_lists(list1, list2):\n    dummy = ListNode(0)\n    current = dummy\n    \n    while list1 and list2:\n        if list1.val <= list2.val:\n            current.next = list1\n            list1 = list1.next\n        else:\n            current.next = list2\n            list2 = list2.next\n        current = current.next\n    \n    current.next = list1 or list2\n    return dummy.next",
    testCases: [
      { input: "[1,2,4], [1,3,4]", expected_output: "[1,1,2,3,4,4]", weight: 0.4 },
      { input: "[], []", expected_output: "[]", weight: 0.2 },
      { input: "[], [0]", expected_output: "[0]", weight: 0.4 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n + m)",
      spaceComplexity: "O(1)",
      constraints: ["Both lists are sorted", "Handle empty lists"]
    },
    gradingRules: {
      testCaseWeight: 0.7,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.1,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 20,
      tags: ["linked-list", "recursion"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook", "Apple"],
      topic: "Linked Lists"
    }
  },
  {
    id: "best-time-to-buy-sell-stock",
    title: "Best Time to Buy and Sell Stock",
    text: "You are given an array prices where prices[i] is the price of a given stock on the ith day.\n\nYou want to maximize your profit by choosing a single day to buy one stock and choosing a different day in the future to sell that stock.\n\nReturn the maximum profit you can achieve from this transaction. If you cannot achieve any profit, return 0.\n\n**Example 1:**\nInput: prices = [7,1,5,3,6,4]\nOutput: 5\nExplanation: Buy on day 2 (price = 1) and sell on day 5 (price = 6), profit = 6-1 = 5.\n\n**Example 2:**\nInput: prices = [7,6,4,3,1]\nOutput: 0\nExplanation: In this case, no transactions are done and the max profit = 0.",
    language: "python",
    starterCode: "def max_profit(prices):\n    # Your code here\n    pass",
    solutionCode: "def max_profit(prices):\n    if not prices:\n        return 0\n    \n    min_price = prices[0]\n    max_profit = 0\n    \n    for price in prices[1:]:\n        if price < min_price:\n            min_price = price\n        else:\n            max_profit = max(max_profit, price - min_price)\n    \n    return max_profit",
    testCases: [
      { input: "[7,1,5,3,6,4]", expected_output: "5", weight: 0.4 },
      { input: "[7,6,4,3,1]", expected_output: "0", weight: 0.3 },
      { input: "[1,2,3,4,5]", expected_output: "4", weight: 0.3 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(1)",
      constraints: ["Single pass solution", "Handle edge cases"]
    },
    gradingRules: {
      testCaseWeight: 0.6,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.2,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 15,
      tags: ["array", "dynamic-programming"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook", "Apple"],
      topic: "Dynamic Programming"
    }
  },
  {
    id: "longest-substring-without-repeating",
    title: "Longest Substring Without Repeating Characters",
    text: "Given a string s, find the length of the longest substring without repeating characters.\n\n**Example 1:**\nInput: s = 'abcabcbb'\nOutput: 3\nExplanation: The answer is 'abc', with the length of 3.\n\n**Example 2:**\nInput: s = 'bbbbb'\nOutput: 1\nExplanation: The answer is 'b', with the length of 1.\n\n**Example 3:**\nInput: s = 'pwwkew'\nOutput: 3\nExplanation: The answer is 'wke', with the length of 3.",
    language: "python",
    starterCode: "def length_of_longest_substring(s):\n    # Your code here\n    pass",
    solutionCode: "def length_of_longest_substring(s):\n    char_map = {}\n    left = 0\n    max_length = 0\n    \n    for right in range(len(s)):\n        if s[right] in char_map:\n            left = max(left, char_map[s[right]] + 1)\n        \n        char_map[s[right]] = right\n        max_length = max(max_length, right - left + 1)\n    \n    return max_length",
    testCases: [
      { input: "'abcabcbb'", expected_output: "3", weight: 0.3 },
      { input: "'bbbbb'", expected_output: "1", weight: 0.3 },
      { input: "'pwwkew'", expected_output: "3", weight: 0.4 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(min(m,n))",
      constraints: ["Sliding window approach", "Handle edge cases"]
    },
    gradingRules: {
      testCaseWeight: 0.6,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.2,
      partialCredit: true
    },
    metadata: {
      difficulty: "MEDIUM",
      estimatedDuration: 25,
      tags: ["hash-table", "string", "sliding-window"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook", "Apple"],
      topic: "Sliding Window"
    }
  },
  {
    id: "container-with-most-water",
    title: "Container With Most Water",
    text: "You are given an integer array height of length n. There are n vertical lines drawn such that the two endpoints of the ith line are (i, 0) and (i, height[i]).\n\nFind two lines that together with the x-axis form a container, such that the container contains the most water.\n\nReturn the maximum amount of water a container can store.\n\n**Example 1:**\nInput: height = [1,8,6,2,5,4,8,3,7]\nOutput: 49\nExplanation: The above vertical lines are represented by array [1,8,6,2,5,4,8,3,7]. In this case, the max area of water (blue section) the container can contain is 49.\n\n**Example 2:**\nInput: height = [1,1]\nOutput: 1",
    language: "python",
    starterCode: "def max_area(height):\n    # Your code here\n    pass",
    solutionCode: "def max_area(height):\n    left, right = 0, len(height) - 1\n    max_water = 0\n    \n    while left < right:\n        width = right - left\n        current_water = min(height[left], height[right]) * width\n        max_water = max(max_water, current_water)\n        \n        if height[left] < height[right]:\n            left += 1\n        else:\n            right -= 1\n    \n    return max_water",
    testCases: [
      { input: "[1,8,6,2,5,4,8,3,7]", expected_output: "49", weight: 0.5 },
      { input: "[1,1]", expected_output: "1", weight: 0.2 },
      { input: "[4,3,2,1,4]", expected_output: "16", weight: 0.3 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(1)",
      constraints: ["Two pointer approach", "Optimal solution required"]
    },
    gradingRules: {
      testCaseWeight: 0.6,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.2,
      partialCredit: true
    },
    metadata: {
      difficulty: "MEDIUM",
      estimatedDuration: 20,
      tags: ["array", "two-pointers"],
      companies: ["Google", "Amazon", "Microsoft", "Facebook"],
      topic: "Two Pointers"
    }
  },
  {
    id: "climbing-stairs",
    title: "Climbing Stairs",
    text: "You are climbing a staircase. It takes n steps to reach the top.\n\nEach time you can either climb 1 or 2 steps. In how many distinct ways can you climb to the top?\n\n**Example 1:**\nInput: n = 2\nOutput: 2\nExplanation: There are two ways to climb to the top.\n1. 1 step + 1 step\n2. 2 steps\n\n**Example 2:**\nInput: n = 3\nOutput: 3\nExplanation: There are three ways to climb to the top.\n1. 1 step + 1 step + 1 step\n2. 1 step + 2 steps\n3. 2 steps + 1 step",
    language: "python",
    starterCode: "def climb_stairs(n):\n    # Your code here\n    pass",
    solutionCode: "def climb_stairs(n):\n    if n <= 2:\n        return n\n    \n    prev2, prev1 = 1, 2\n    \n    for i in range(3, n + 1):\n        current = prev1 + prev2\n        prev2, prev1 = prev1, current\n    \n    return prev1",
    testCases: [
      { input: "2", expected_output: "2", weight: 0.3 },
      { input: "3", expected_output: "3", weight: 0.3 },
      { input: "5", expected_output: "8", weight: 0.4 }
    ],
    evaluationCriteria: {
      timeComplexity: "O(n)",
      spaceComplexity: "O(1)",
      constraints: ["Dynamic programming approach", "Space optimized solution"]
    },
    gradingRules: {
      testCaseWeight: 0.7,
      codeQualityWeight: 0.2,
      efficiencyWeight: 0.1,
      partialCredit: true
    },
    metadata: {
      difficulty: "EASY",
      estimatedDuration: 15,
      tags: ["math", "dynamic-programming", "memoization"],
      companies: ["Google", "Amazon", "Microsoft", "Apple"],
      topic: "Dynamic Programming"
    }
  }
];

// Add more questions for different topics and companies
export const getQuestionsByTopic = (topic: string): PredefinedQuestion[] => {
  return PREDEFINED_QUESTIONS.filter(q => q.metadata.topic === topic);
};

export const getQuestionsByCompany = (company: string): PredefinedQuestion[] => {
  return PREDEFINED_QUESTIONS.filter(q => q.metadata.companies.includes(company));
};

export const getQuestionsByDifficulty = (difficulty: string): PredefinedQuestion[] => {
  return PREDEFINED_QUESTIONS.filter(q => q.metadata.difficulty === difficulty);
};

export const searchQuestions = (query: string): PredefinedQuestion[] => {
  const lowercaseQuery = query.toLowerCase();
  return PREDEFINED_QUESTIONS.filter(q =>
    q.title.toLowerCase().includes(lowercaseQuery) ||
    q.metadata.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)) ||
    q.metadata.companies.some(company => company.toLowerCase().includes(lowercaseQuery)) ||
    q.metadata.topic.toLowerCase().includes(lowercaseQuery)
  );
};
