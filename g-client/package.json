{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "inspect": "cross-env NODE_OPTIONS='--inspect' next dev", "build": "next build", "start": "next start", "lint": "next lint --fix", "format": "prettier --write ."}, "dependencies": {"@auth/core": "^0.34.2", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-table": "^8.20.6", "@types/js-cookie": "^3.0.6", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "framer-motion": "^12.9.4", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "monaco-editor": "^0.52.2", "motion": "^12.10.0", "next": "15.1.0", "next-auth": "^4.24.11", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "react-markdown": "^10.1.0", "react-quill": "^2.0.0", "react-quill-new": "^3.4.6", "react-spinners": "^0.15.0", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.3", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.8", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "eslint": "^9.20.0", "eslint-config-next": "^15.1.6", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "postcss": "^8", "prettier": "^3.5.0", "tailwindcss": "^3.4.1", "typescript": "^5"}}