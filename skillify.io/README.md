# Skillify

## Introduction

## Table of Contents

- [Skillify](#skillify)
  - [Introduction](#introduction)
  - [Table of Contents](#table-of-contents)
  - [Prerequisites](#prerequisites)
  - [Quick Start](#quick-start)
  - [API Documentation](#api-documentation)
    - [Swagger UI](#swagger-ui)
    - [Authentication](#authentication)
    - [Organization Management](#organization-management)
    - [Job Management](#job-management)
    - [Test Management](#test-management)
    - [Authorization Test Endpoints](#authorization-test-endpoints)

## Prerequisites

Before you begin, ensure you have the following installed:

*   Java 23
*   Docker (for PostgreSQL)

## Quick Start

Follow these steps to get Skillify up and running:

1.  **Start PostgreSQL using Docker:**

    ```bash
    docker run --name skillify-db \
      -e POSTGRES_USER=postgres \
      -e POSTGRES_PASSWORD=root \
      -e POSTGRES_DB=skillify \
      -p 5432:5432 \
      -d postgres:latest
    ```

2.  **Create a `.env` file** in the project root with the following content:

    ```properties
    POSTGRES_HOST=localhost
    POSTGRES_PORT=5432
    POSTGRES_USER=postgres
    POSTGRES_PASSWORD=root
    POSTGRES_DB=skillify
    JWT_SECRET=your_jwt_secret
    ```

3.  **Configure Flyway:**
    Create a `flyway.conf` file in the project root with the following content:

    ```properties
    flyway.url=*****************************************
    flyway.user=postgres
    flyway.password=root
    flyway.locations=classpath:db/migration
    ```

    Replace the properties with your own values.

4.  **Build and Run the Application:**

    ```bash
    ./mvnw clean install
    ./mvnw spring-boot:run
    ```

    The application will be available at `http://localhost:8080`.

## API Documentation

### Swagger UI

You can access the Swagger UI for a visual representation of the API endpoints and to interact with the API directly.

*   **URL:** `http://localhost:8080/swagger-ui/index.html`

### Authentication

<details>
<summary><b>Login</b></summary>

*   **Endpoint:** `/api/auth/login`
*   **Method:** POST
*   **Description:** Authenticates a user, sets a JWT in an HttpOnly cookie, and returns user details.

*   **Example Response:**

    A cookie named `JWT_COOKIE_NAME` is set in the response headers.

    ```json
    {
        "status": "OK",
        "success": true,
        "data": {
            "id": "b4b57511-e088-491a-9b24-bb2c47479ea4",
            "email": "<EMAIL>",
            "username": "hello",
            "lastLogin": "2025-02-06T02:44:48.464616887+02:00",
            "createdAt": "2025-02-05T21:55:09.172466Z",
            "roles": [
                {
                    "id": 3,
                    "name": "ROLE_ADMIN",
                    "permissions": [
                        {
                            "permissionName": "ROLE_MANAGE",
                            "description": "Manage roles and permissions",
                            "category": "ADMINISTRATION"
                        },
                        // ...other permissions
                    ]
                }
            ]
        },
        "error": null,
        "timestamp": "2025-02-06T02:44:48.591853244"
    }
    ```
</details>

<details>
<summary><b>Register</b></summary>

*   **Endpoint:** `/api/auth/register`
*   **Method:** POST
*   **Description:** Creates a new user. No JWT cookie is set during registration.

*   **Example Response:**

    ```json
    {
        "status": "OK",
        "success": true,
        "data": {
            "id": "ffaab8e4-e67d-4db8-be05-65678da7fa0c",
            "email": "<EMAIL>",
            "username": "helloworld",
            "lastLogin": "2025-02-06T02:45:43.793323767+02:00",
            "createdAt": "2025-02-06T02:45:43.793323767+02:00",
            "roles": [
                {
                    "id": 3,
                    "name": "ROLE_ADMIN",
                    "permissions": [
                      // permissions for the role
                    ]
                }
            ]
        },
        "error": null,
        "timestamp": "2025-02-06T02:45:43.808194218"
    }
    ```
</details>

<details>
<summary><b>Logout</b></summary>

*   **Endpoint:** `/api/auth/logout`
*   **Method:** POST
*   **Description:** Logs out the authenticated user and clears the JWT cookie.

*   **Example Response:**

    ```json
    {
        "status": "OK",
        "success": true,
        "data": "Logged out successfully",
        "error": null,
        "timestamp": "2025-02-06T02:45:55.30313572"
    }
    ```
</details>

<details>
<summary><b>Refresh Token</b></summary>

*   **Endpoint:** `/api/auth/token/refresh`
*   **Method:** POST
*   **Description:** Refreshes the JWT token using a valid refresh token from cookies.

*   **Example Response:**

    ```json
    {
        "status": "OK",
        "success": true,
        "data": "Token refreshed successfully",
        "error": null,
        "timestamp": "..."
    }
    ```
</details>

### Organization Management

<details>
<summary><b>Create Organization</b></summary>

*   **Endpoint:** `/api/orgs`
*   **Method:** POST
*   **Description:** Creates a new organization. Requires authentication.

*   **Request Body:**

    ```json
    {
      "name": "Skillify Inc."
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "name": "Skillify Inc.",
        "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "memberCount": 1
      },
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Organization Details</b></summary>

*   **Endpoint:** `/api/orgs/{orgId}`
*   **Method:** GET
*   **Description:** Retrieves details of a specific organization. Requires `MANAGE_ORG` permission.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "name": "Skillify Inc.",
        "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "memberCount": 5
      },
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Update Organization</b></summary>

*   **Endpoint:** `/api/orgs/{orgId}`
*   **Method:** PUT
*   **Description:** Updates an existing organization. Requires `MANAGE_ORG` permission.

*   **Request Body:**

    ```json
    {
      "name": "Updated Organization Name"
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "name": "Updated Organization Name",
        "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "memberCount": 5
      },
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Delete Organization</b></summary>

*   **Endpoint:** `/api/orgs/{orgId}`
*   **Method:** DELETE
*   **Description:** Deletes an organization. Requires `MANAGE_ORG` permission.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": "Organization deleted successfully",
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get User Organizations</b></summary>

*   **Endpoint:** `/api/orgs/user/current`
*   **Method:** GET
*   **Description:** Retrieves all organizations the current user belongs to.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
          "name": "Skillify Inc.",
          "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
          "memberCount": 1
        }
      ],
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Add Member to Organization</b></summary>

*   **Endpoint:** `/api/orgs/{orgId}/members`
*   **Method:** POST
*   **Description:** Adds a member to an organization. Requires `MANAGE_ORG` permission.

*   **Request Body:**

    ```json
    {
      "userEmail": "<EMAIL>",
      "roleName": "ROLE_ORG_INTERVIEWER"
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": null,
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

### Job Management

<details>
<summary><b>Create Job</b></summary>

*   **Endpoint:** `/api/jobs`
*   **Method:** POST
*   **Description:** Creates a new job posting for an organization. Requires `MANAGE_JOB` permission.

*   **Request Body:**

    ```json
    {
      "title": "Software Engineer",
      "description": "Develop and maintain software applications.",
      "organizationId": "123e4567-e89b-12d3-a456-426614174000"
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "456a7890-cdef-3412-b567-890abcdeffed",
        "title": "Software Engineer",
        "description": "Develop and maintain software applications.",
        "organizationId": "123e4567-e89b-12d3-a456-426614174000",
        "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "updatedBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "updatedAt": "2024-01-01T12:00:00Z",
        "createdAt": "2024-01-01T12:00:00Z",
        "active": true
      },
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Update Job</b></summary>

*   **Endpoint:** `/api/jobs/{jobId}`
*   **Method:** PUT
*   **Description:** Updates an existing job posting. Requires `MANAGE_JOB` permission.

*   **Request Body:**

    ```json
    {
      "title": "Senior Software Engineer",
      "description": "Lead the development and maintenance of software applications.",
      "isActive": true
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "456a7890-cdef-3412-b567-890abcdeffed",
        "title": "Senior Software Engineer",
        "description": "Lead the development and maintenance of software applications.",
        "organizationId": "123e4567-e89b-12d3-a456-426614174000",
        "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "updatedBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "updatedAt": "2024-01-01T12:00:00Z",
        "createdAt": "2024-01-01T12:00:00Z",
        "active": true
      },
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Job Details</b></summary>

*   **Endpoint:** `/api/jobs/{jobId}`
*   **Method:** GET
*   **Description:** Retrieves details of a specific job posting.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "456a7890-cdef-3412-b567-890abcdeffed",
        "title": "Senior Software Engineer",
        "description": "Lead the development and maintenance of software applications.",
        "organizationId": "123e4567-e89b-12d3-a456-426614174000",
        "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "updatedBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
        "updatedAt": "2024-01-01T12:00:00Z",
        "createdAt": "2024-01-01T12:00:00Z",
        "active": true
      },
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Organization Jobs</b></summary>

*   **Endpoint:** `/api/jobs/org/{organizationId}`
*   **Method:** GET
*   **Description:** Retrieves all jobs for a specific organization.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "456a7890-cdef-3412-b567-890abcdeffed",
          "title": "Senior Software Engineer",
          "description": "Lead the development and maintenance of software applications.",
          "organizationId": "123e4567-e89b-12d3-a456-426614174000",
          "createdBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
          "updatedBy": "f1e2d3c4-b5a6-7890-1234-567890abcdef",
          "updatedAt": "2024-01-01T12:00:00Z",
          "createdAt": "2024-01-01T12:00:00Z",
          "active": true
        }
      ],
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Delete Job</b></summary>

*   **Endpoint:** `/api/jobs/{jobId}`
*   **Method:** DELETE
*   **Description:** Deletes an existing job posting. Requires `MANAGE_JOB` permission.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": "Job deleted successfully",
      "error": null,
      "timestamp": "2024-01-01T12:00:00Z"
    }
    ```
</details>

### Test Management

<details>
<summary><b>Create Test</b></summary>

*   **Endpoint:** `/api/tests`
*   **Method:** POST
*   **Description:** Creates a new test for a specific job. Requires `MANAGE_TEST` permission for the job.

*   **Request Body:**

    ```json
    {
      "name": "Java Developer Assessment",
      "description": "Assessment for Java Developer position",
      "timeLimit": 60,
      "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "startTime": "2025-01-01T09:00:00Z",
      "endTime": "2025-01-31T18:00:00Z"
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",
        "name": "Java Developer Assessment",
        "description": "Assessment for Java Developer position",
        "timeLimit": 60,
        "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "jobTitle": "Senior Java Developer",
        "startTime": "2025-01-01T09:00:00Z",
        "endTime": "2025-01-31T18:00:00Z",
        "status": "DRAFT",
        "createdBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
        "updatedBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
        "createdAt": "2025-01-01T09:00:00Z",
        "updatedAt": "2025-01-01T09:00:00Z"
      },
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Update Test</b></summary>

*   **Endpoint:** `/api/tests/{id}`
*   **Method:** PUT
*   **Description:** Updates an existing test. Requires `MANAGE_TEST` permission for the job.

*   **Request Body:**

    ```json
    {
      "name": "Updated Java Developer Assessment",
      "description": "Updated assessment for Java Developer position",
      "timeLimit": 90,
      "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "startTime": "2025-01-01T09:00:00Z",
      "endTime": "2025-01-31T18:00:00Z",
      "status": "PUBLISHED"
    }
    ```

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",
        "name": "Updated Java Developer Assessment",
        "description": "Updated assessment for Java Developer position",
        "timeLimit": 90,
        "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "jobTitle": "Senior Java Developer",
        "startTime": "2025-01-01T09:00:00Z",
        "endTime": "2025-01-31T18:00:00Z",
        "status": "PUBLISHED",
        "createdBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
        "updatedBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
        "createdAt": "2025-01-01T09:00:00Z",
        "updatedAt": "2025-01-01T09:00:00Z"
      },
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Test by ID</b></summary>

*   **Endpoint:** `/api/tests/{id}`
*   **Method:** GET
*   **Description:** Retrieves a test by its ID. Requires `MANAGE_TEST` permission for the test.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": {
        "id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",
        "name": "Java Developer Assessment",
        "description": "Assessment for Java Developer position",
        "timeLimit": 60,
        "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
        "jobTitle": "Senior Java Developer",
        "startTime": "2025-01-01T09:00:00Z",
        "endTime": "2025-01-31T18:00:00Z",
        "status": "PUBLISHED",
        "createdBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
        "updatedBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
        "createdAt": "2025-01-01T09:00:00Z",
        "updatedAt": "2025-01-01T09:00:00Z"
      },
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Tests by Job ID</b></summary>

*   **Endpoint:** `/api/tests/job/{jobId}`
*   **Method:** GET
*   **Description:** Retrieves all tests for a specific job. Requires `MANAGE_TEST` permission for the job.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",
          "name": "Java Developer Assessment",
          "description": "Assessment for Java Developer position",
          "timeLimit": 60,
          "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
          "jobTitle": "Senior Java Developer",
          "startTime": "2025-01-01T09:00:00Z",
          "endTime": "2025-01-31T18:00:00Z",
          "status": "PUBLISHED",
          "createdBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
          "updatedBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
          "createdAt": "2025-01-01T09:00:00Z",
          "updatedAt": "2025-01-01T09:00:00Z"
        }
      ],
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Tests by Status</b></summary>

*   **Endpoint:** `/api/tests/status/{status}`
*   **Method:** GET
*   **Description:** Retrieves all tests with a specific status. Requires `ROLE_ADMIN`.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",
          "name": "Java Developer Assessment",
          "description": "Assessment for Java Developer position",
          "timeLimit": 60,
          "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
          "jobTitle": "Senior Java Developer",
          "startTime": "2025-01-01T09:00:00Z",
          "endTime": "2025-01-31T18:00:00Z",
          "status": "PUBLISHED",
          "createdBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
          "updatedBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
          "createdAt": "2025-01-01T09:00:00Z",
          "updatedAt": "2025-01-01T09:00:00Z"
        }
      ],
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Get Tests by Job ID and Status</b></summary>

*   **Endpoint:** `/api/tests/job/{jobId}/status?status={status}`
*   **Method:** GET
*   **Description:** Retrieves all tests for a specific job with a specific status. Requires `MANAGE_TEST` permission for the job.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": [
        {
          "id": "b1c2d3e4-f5a6-7890-1234-567890abcdef",
          "name": "Java Developer Assessment",
          "description": "Assessment for Java Developer position",
          "timeLimit": 60,
          "jobId": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
          "jobTitle": "Senior Java Developer",
          "startTime": "2025-01-01T09:00:00Z",
          "endTime": "2025-01-31T18:00:00Z",
          "status": "PUBLISHED",
          "createdBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
          "updatedBy": "c1d2e3f4-a5b6-7890-1234-567890abcdef",
          "createdAt": "2025-01-01T09:00:00Z",
          "updatedAt": "2025-01-01T09:00:00Z"
        }
      ],
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Delete Test</b></summary>

*   **Endpoint:** `/api/tests/{id}`
*   **Method:** DELETE
*   **Description:** Deletes a test by its ID. Requires `MANAGE_TEST` permission for the test.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": null,
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

<details>
<summary><b>Count Tests by Job ID</b></summary>

*   **Endpoint:** `/api/tests/job/{jobId}/count`
*   **Method:** GET
*   **Description:** Counts the number of tests for a specific job. Requires `MANAGE_TEST` permission for the job.

*   **Example Response:**

    ```json
    {
      "status": "OK",
      "success": true,
      "data": 5,
      "error": null,
      "timestamp": "2025-01-01T09:00:00Z"
    }
    ```
</details>

### Authorization Test Endpoints

<details>
<summary><b>Public Endpoint</b></summary>

*   **Endpoint:** `/api/test/public`
*   **Method:** GET
*   **Description:** Accessible to all users, authenticated or not.

*   **Example Response:**

    ```text
    This endpoint is available for everyone, even unauthenticated users.
    ```
</details>

<details>
<summary><b>Open Endpoint</b></summary>

*   **Endpoint:** `/api/test/open`
*   **Method:** GET
*   **Description:** Accessible to authenticated users.

*   **Example Response:**

    ```text
    This endpoint is available for authenticated users.
    ```
</details>

<details>
<summary><b>Admin Endpoint</b></summary>

*   **Endpoint:** `/api/test/admin`
*   **Method:** GET
*   **Description:** Accessible only to admins.

*   **Example Response:**

    ```text
    Hello Admin! You have access to admin resources.
    ```
</details>
