package io.skillify.mappers;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import io.skillify.dtos.user.UserDto;
import io.skillify.models.user.User;

@Mapper(componentModel = "spring", uses = { RoleMapper.class })
public interface UserMapper {

    // Map User -> UserDto
    @Mapping(target = "roles", source = "roles")
    @Mapping(target = "username", source = "user",qualifiedByName = "extractUsername")
    UserDto userToUserDto(User user);

    @Named("extractUsername")
    default String extractUsername(User user) {
        return user.getName();
   }

    // Map UserDto -> User
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "password", ignore = true)
    @Mapping(target = "emailVerificationToken", ignore = true)
    @Mapping(target = "emailVerificationTokenExpiry", ignore = true)
    @Mapping(target = "passwordResetToken", ignore = true)
    @Mapping(target = "passwordResetTokenExpiry", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "verified", ignore = true)
    @Mapping(target = "orgMemberships", ignore = true)
    User userDtoToUser(UserDto userDto);
}
