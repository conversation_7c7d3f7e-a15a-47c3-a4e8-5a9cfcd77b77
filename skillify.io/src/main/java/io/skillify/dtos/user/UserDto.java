package io.skillify.dtos.user;

import java.io.Serializable;
import java.time.OffsetDateTime;
import java.util.Set;
import java.util.UUID;

import io.skillify.dtos.role.RoleDto;
import lombok.Builder;
import lombok.Getter;
import lombok.Value;

@Value
@Getter
@Builder
public class UserDto implements Serializable {
    UUID id;
    String email;
    String username;
    OffsetDateTime lastLogin;
    OffsetDateTime createdAt;
    Set<RoleDto> roles;
}
