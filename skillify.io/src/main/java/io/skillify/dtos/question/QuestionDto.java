package io.skillify.dtos.question;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.JsonNode;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

public class QuestionDto {

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BaseQuestion {
        @Schema(description = "Question type (e.g., 'MULTIPLE_CHOICE', 'CODING', 'TEXT')")
        private String type;

        @Schema(description = "The question text or prompt")
        private String text;

        @Schema(description = "Available options for the question (for multiple choice)")
        private JsonNode options;

        @Schema(description = "The correct answer to the question")
        private JsonNode correctAnswer;

        @Schema(description = "Question difficulty level (e.g., 'EASY', 'MEDIUM', 'HARD')")
        private String difficulty;

        @Min(value = 0, message = "Order must be non-negative")
        @Schema(description = "Order/position of the question within the test")
        private Integer order;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "QuestionResponse", description = "Question response")
    @EqualsAndHashCode(callSuper = true)
    public static class Response extends BaseQuestion {
        @Schema(description = "Unique identifier of the question")
        private UUID id;

        @Schema(description = "Unique identifier of the test this question belongs to")
        private UUID testId;

        @Schema(description = "When the question was created")
        private OffsetDateTime createdAt;

        @Schema(description = "When the question was last updated")
        private OffsetDateTime updatedAt;

        @Schema(description = "Identifier of the user who created the question")
        private String createdBy;

        @Schema(description = "Identifier of the user who last updated the question")
        private String updatedBy;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "QuestionCreateRequest", description = "Question creation request")
    @EqualsAndHashCode(callSuper = true)
    public static class CreateQuestionRequest extends BaseQuestion {
        @NotNull(message = "Test ID is required")
        @Schema(description = "Unique identifier of the test this question belongs to", required = true)
        private UUID testId;

        @NotNull(message = "Question type is required")
        @Schema(description = "Question type (e.g., 'MULTIPLE_CHOICE', 'CODING', 'TEXT')", required = true)
        private String type;

        @NotBlank(message = "Question text is required")
        @Schema(description = "The question text or prompt", required = true)
        private String text;

        @NotNull(message = "Question difficulty is required")
        @Schema(description = "Question difficulty level (e.g., 'EASY', 'MEDIUM', 'HARD')", required = true)
        private String difficulty;
    }

    @Data
    @SuperBuilder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "QuestionUpdateRequest", description = "Question update request")
    @EqualsAndHashCode(callSuper = true)
    public static class UpdateQuestionRequest extends BaseQuestion {
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchCreateQuestionsRequest", description = "Request to create multiple questions in one operation")
    public static class BatchCreateQuestionsRequest {
        @NotNull(message = "Test ID is required")
        @Schema(description = "Unique identifier of the test these questions belong to", required = true)
        private UUID testId;

        @NotEmpty(message = "At least one question is required")
        @Size(min = 1, message = "At least one question is required")
        @Valid
        @Schema(description = "List of questions to create", required = true)
        private List<BaseQuestion> questions;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchCreateQuestionsResponse", description = "Response after creating multiple questions")
    public static class BatchCreateQuestionsResponse {
        @Schema(description = "Unique identifier of the test")
        private UUID testId;

        @Schema(description = "Number of questions successfully created")
        private Integer createdQuestions;

        @Schema(description = "Common metadata for all created questions")
        private QuestionMetadata metadata;

        @Schema(description = "List of created questions")
        private List<QuestionContent> questions;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Common metadata for questions created in batch")
    public static class QuestionMetadata {
        @Schema(description = "When the questions were created")
        private OffsetDateTime createdAt;

        @Schema(description = "When the questions were last updated")
        private OffsetDateTime updatedAt;

        @Schema(description = "Identifier of the user who created the questions")
        private String createdBy;

        @Schema(description = "Identifier of the user who last updated the questions")
        private String updatedBy;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Question content for batch operations")
    public static class QuestionContent {
        @Schema(description = "Unique identifier of the question")
        private UUID id;

        @Schema(description = "Question type (e.g., 'MCQ')")
        private String type;

        @Schema(description = "The question text or prompt")
        private String text;

        @Schema(description = "Options for the question")
        private JsonNode options;

        @Schema(description = "The correct answer to the question")
        private JsonNode correctAnswer;

        @Schema(description = "Question difficulty level (e.g., 'EASY', 'MEDIUM', 'HARD')")
        private String difficulty;

        @Schema(description = "Order/position of the question within the test")
        private Integer order;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchDeleteQuestionsRequest", description = "Request to delete multiple questions")
    public static class BatchDeleteQuestionsRequest {
        @NotNull(message = "Test ID is required")
        @Schema(description = "Unique identifier of the test these questions belong to", required = true)
        private UUID testId;

        @NotEmpty(message = "At least one question ID is required")
        @Size(min = 1, message = "At least one question ID is required")
        @Schema(description = "List of question IDs to delete", required = true)
        private List<UUID> questionIds;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchDeleteQuestionsResponse", description = "Response after deleting multiple questions")
    public static class BatchDeleteQuestionsResponse {
        @Schema(description = "Unique identifier of the test")
        private UUID testId;

        @Schema(description = "Number of questions successfully deleted")
        private Integer deletedQuestions;

        @Schema(description = "List of question IDs that were not found or could not be deleted")
        private List<UUID> failedIds;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "Represents a single question within a batch update request")
    @EqualsAndHashCode(callSuper = true)
    public static class QuestionUpdateItem extends BaseQuestion {
        @Schema(description = "Unique identifier of the question. If null, it's a new question.")
        private UUID id;

        @Builder.Default
        @Schema(description = "Flag indicating if the question should be deleted. Defaults to false.")
        private boolean isDeleted = false;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchUpdateQuestionsRequest", description = "Request to update multiple questions in one operation")
    public static class BatchUpdateQuestionsRequest {
        @NotNull(message = "Test ID is required")
        @Schema(description = "Unique identifier of the test these questions belong to", required = true)
        private UUID testId;

        @NotEmpty(message = "At least one question item is required")
        @Size(min = 1, message = "At least one question item is required")
        @Valid
        @Schema(description = "List of question states (create, update, delete)", required = true)
        private List<QuestionUpdateItem> questions;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(name = "BatchUpdateQuestionsResponse", description = "Response after updating multiple questions")
    public static class BatchUpdateQuestionsResponse {
        @Schema(description = "Unique identifier of the test")
        private UUID testId;

        @Schema(description = "Number of questions successfully created")
        private int createdCount;

        @Schema(description = "Number of questions successfully updated")
        private int updatedCount;

        @Schema(description = "Number of questions successfully deleted")
        private int deletedCount;

        @Schema(description = "List of questions after the batch operation")
        private List<Response> resultingQuestions;
    }
}
