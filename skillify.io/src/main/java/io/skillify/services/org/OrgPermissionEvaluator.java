package io.skillify.services.org;

import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.models.job.Job;
import io.skillify.models.org.OrgPermission;
import io.skillify.models.test.Test;
import io.skillify.models.test.TestAssignment;
import io.skillify.models.user.User;
import io.skillify.repositories.job.JobRepository;
import io.skillify.repositories.question.QuestionRepository;
import io.skillify.repositories.test.TestAssignmentRepository;
import io.skillify.repositories.test.TestRepository;
import io.skillify.repositories.user.UserRepository;
import lombok.RequiredArgsConstructor;

@Component("orgPermissionEvaluator")
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class OrgPermissionEvaluator {

    private static final Logger logger = LoggerFactory.getLogger(OrgPermissionEvaluator.class);
    private static final String ROLE_ADMIN = "ROLE_ADMIN";

    private final OrgRoleService orgRoleService;
    private final UserRepository userRepository;
    private final TestRepository testRepository;
    private final JobRepository jobRepository;
    private final TestAssignmentRepository testAssignmentRepository;
    private final QuestionRepository questionRepository;

    /**
     * Checks if the current user has a specific permission for an organization
     */
    public boolean hasPermission(UUID orgId, String permission) {
        // if (isAdmin()) {
        // return true;
        // }

        Optional<User> userOpt = getCurrentUser();
        if (userOpt.isEmpty()) {
            return false;
        }

        User user = userOpt.get();
        logger.debug("Evaluating permission '{}' for user '{}' in organization '{}'",
                permission, user.getId(), orgId);

        Set<OrgPermission> permissions = orgRoleService.getUserOrganizationPermissions(orgId, user);

        boolean hasPerm = permissions.stream()
                .anyMatch(p -> p.getName().equals(permission));

        if (!hasPerm) {
            logger.debug("Permission '{}' not found for user '{}'", permission, user.getId());
            logger.debug("User permissions: {}", (permissions.stream()
                    .map(OrgPermission::getName)
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("None")));
        }

        return hasPerm;
    }

    /**
     * Checks if the current user has a specific permission for a job
     */
    public boolean hasPermissionForJob(UUID jobId, String permission) {
        // if (isAdmin()) {
        // return true;
        // }

        Optional<User> userOpt = getCurrentUser();
        if (userOpt.isEmpty()) {
            return false;
        }

        Optional<Job> jobOpt = jobRepository.findById(jobId);
        if (jobOpt.isEmpty()) {
            logger.warn("Job {} not found", jobId);
            return false;
        }

        Job job = jobOpt.get();
        return hasPermission(job.getOrganization().getId(), permission);
    }

    /**
     * Checks if the current user has a specific permission for a test
     */
    public boolean hasPermissionForTest(UUID testId, String permission) {
        // if (isAdmin()) {
        // return true;
        // }

        Optional<User> userOpt = getCurrentUser();
        if (userOpt.isEmpty()) {
            return false;
        }

        Optional<Test> testOpt = testRepository.findByIdWithJob(testId);
        if (testOpt.isEmpty()) {
            logger.warn("Test {} not found", testId);
            return false;
        }

        Test test = testOpt.get();
        return hasPermission(test.getJob().getOrganization().getId(), permission);
    }

    /**
     * Checks if the current user can take a test
     */
    public boolean canTakeTest(UUID testId) {
        logger.debug("Checking if user can take test {}", testId);
        Optional<User> userOpt = getCurrentUser();
        if (userOpt.isEmpty()) {
            return false;
        }

        Optional<Test> testOpt = testRepository.findByIdWithJob(testId);
        if (testOpt.isEmpty()) {
            logger.warn("Test {} not found", testId);
            return false;
        }

        Test test = testOpt.get();
        return testAssignmentRepository.findByTestIdAndCandidateId(test.getId(), userOpt.get().getId()).isPresent();
    }

    /**
     * Checks if the current user has a specific permission for a question
     */
    public boolean hasPermissionForQuestion(UUID questionId, String permission) {
        Optional<User> userOpt = getCurrentUser();
        if (userOpt.isEmpty()) {
            logger.debug("No authenticated user found");
            return false;
        }

        UUID orgId = questionRepository.getOrgIdByQuestionId(questionId);
        if (orgId == null) {
            logger.warn("Question {} not found or organization ID is null", questionId);
            return false;
        }

        logger.debug("Checking permission {} for question {} with organization {}",
            permission, questionId, orgId);
        return hasPermission(orgId, permission);
    }

    /**
     * Checks if the current user has a specific permission for a test assignment
     */
    public boolean hasPermissionForTestAssignment(UUID testAssignmentId, String permission) {
        // if (isAdmin()) {
        //      return true;
        // }

        Optional<User> userOpt = getCurrentUser();
        if (userOpt.isEmpty()) {
            return false;
        }

        Optional<TestAssignment> testAssignmentOpt = testAssignmentRepository.findById(testAssignmentId);
        if (testAssignmentOpt.isEmpty()) {
            logger.warn("Test assignment {} not found", testAssignmentId);
            return false;
        }

        TestAssignment testAssignment = testAssignmentOpt.get();
        return hasPermission(testAssignment.getTest().getJob().getOrganization().getId(), permission);
    }

    /**
     * Gets the current authenticated user
     */
    private Optional<User> getCurrentUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            logger.warn("Authentication is null");
            return Optional.empty();
        }

        String username = authentication.getName();
        Optional<User> userOpt = userRepository.findByEmail(username);

        if (userOpt.isEmpty()) {
            logger.warn("User not found with username/email: {}", username);
        }

        return userOpt;
    }

    /**
     * Checks if the current user is an admin
     */
    private boolean isAdmin() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }

        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals(ROLE_ADMIN));

        if (isAdmin) {
            logger.debug("User {} is an admin, granting permission", authentication.getName());
        }

        return isAdmin;
    }
}
