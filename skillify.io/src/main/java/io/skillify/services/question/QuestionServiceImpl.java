package io.skillify.services.question;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.question.QuestionDto;
import io.skillify.dtos.question.QuestionDto.BaseQuestion;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchDeleteQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchDeleteQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.CreateQuestionRequest;
import io.skillify.dtos.question.QuestionDto.QuestionMetadata;
import io.skillify.dtos.question.QuestionDto.QuestionUpdateItem;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.QuestionMapper;
import io.skillify.models.question.Question;
import io.skillify.models.test.Test;
import io.skillify.repositories.question.QuestionRepository;
import io.skillify.repositories.test.TestRepository;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
@Transactional
public class QuestionServiceImpl implements QuestionService {
    Logger logger = LoggerFactory.getLogger(QuestionServiceImpl.class);

    private final QuestionRepository questionRepository;
    private final TestRepository testRepository;
    private final QuestionMapper questionMapper;

    @Override
    public QuestionDto.Response createQuestion(CreateQuestionRequest request) {
        logger.info("Creating question for test with ID: {}", request.getTestId());

        Test test = testRepository.findById(request.getTestId())
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + request.getTestId()));

        Question question = questionMapper.toEntity(request);
        question.setTest(test);

        // If the order is specified, check if it is already taken
        if (question.getOrder() != null) {
            if (questionRepository.existsByTestIdAndOrder(request.getTestId(), question.getOrder())) {
                logger.info("Question with order {} already exists, shifting questions", question.getOrder());
                // Shift all questions with order >= the specified order up by one
                questionRepository.incrementOrdersGreaterThanOrEqual(request.getTestId(), question.getOrder());
            }
        }

        // If order is not specified, set it to the next available order
        if (question.getOrder() == null) {
            Integer maxOrder = questionRepository.findMaxOrderByTestId(request.getTestId());
            question.setOrder(maxOrder != null ? maxOrder + 1 : 1);
        }

        Question savedQuestion = questionRepository.save(question);
        return questionMapper.toDto(savedQuestion);
    }

    @Override
    @Transactional(readOnly = true)
    public QuestionDto.Response getQuestion(UUID id) {
        logger.info("Fetching question with ID: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Question not found with ID: " + id));

        return questionMapper.toDto(question);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTest(UUID testId) {
        logger.info("Fetching questions for test with ID: {}", testId);

        List<Question> questions = questionRepository.findByTestId(testId);
        return questionMapper.toDtoList(questions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTestOrdered(UUID testId) {
        logger.info("Fetching ordered questions for test with ID: {}", testId);

        List<Question> questions = questionRepository.findByTestIdOrderByOrder(testId);
        return questionMapper.toDtoList(questions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTestAndType(UUID testId, String type) {
        logger.info("Fetching questions for test with ID: {} and type: {}", testId, type);

        List<Question> questions = questionRepository.findByTestIdAndType(testId, type);
        return questionMapper.toDtoList(questions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<QuestionDto.Response> getQuestionsByTestAndDifficulty(UUID testId, String difficulty) {
        logger.info("Fetching questions for test with ID: {} and difficulty: {}", testId, difficulty);

        List<Question> questions = questionRepository.findByTestIdAndDifficulty(testId, difficulty);
        return questionMapper.toDtoList(questions);
    }

    @Override
    public QuestionDto.Response updateQuestion(UUID id, BaseQuestion request) {
        logger.info("Updating question with ID: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Question not found with ID: " + id));

        questionMapper.updateEntity(question, request);

        Question updatedQuestion = questionRepository.saveAndFlush(question);
        logger.info("Successfully updated question with ID: {}", id);

        return questionMapper.toDto(updatedQuestion);
    }

    @Override
    public void deleteQuestion(UUID id) {
        logger.info("Deleting question with ID: {}", id);

        if (!questionRepository.existsById(id)) {
            throw new ResourceNotFoundException("Question not found with ID: " + id);
        }

        // Get Test Id and Order of the question
        UUID testId = questionRepository.getTestIdByQuestionId(id);
        Integer deletedQuestionOrder = questionRepository.getOrderByQuestionId(id);

        // Delete the question
        questionRepository.deleteById(id);
        logger.info("Deleted question with ID: {}", id);

        // Reorder remaining questions
        questionRepository.decrementOrdersGreaterThan(testId, deletedQuestionOrder);
        logger.info("Reordered questions for test with ID: {}", testId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countQuestionsByTest(UUID testId) {
        logger.info("Counting questions for test with ID: {}", testId);

        return questionRepository.countByTestId(testId);
    }

    @Override
    @Transactional
    public BatchCreateQuestionsResponse batchCreateQuestions(BatchCreateQuestionsRequest request) {
        logger.info("Batch creating {} questions for test with ID: {}",
                request.getQuestions().size(), request.getTestId());

        Test test = testRepository.findById(request.getTestId())
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + request.getTestId()));

        // Get the max order number for this test
        Integer maxOrder = questionRepository.findMaxOrderByTestId(request.getTestId());
        int nextOrder = maxOrder != null ? maxOrder + 1 : 1;

        List<Question> questions = new ArrayList<>();

        // Process each question in the batch
        for (QuestionDto.BaseQuestion baseQuestion : request.getQuestions()) {
            Question question = questionMapper.baseToEntity(baseQuestion);
            question.setTest(test);

            // If order is not specified, assign the next available order
            if (question.getOrder() == null) {
                question.setOrder(nextOrder++);
            } else {
                if (questionRepository.existsByTestIdAndOrder(request.getTestId(), question.getOrder())) {
                    // Shift all questions with order >= the specified order up by one
                    questionRepository.incrementOrdersGreaterThanOrEqual(request.getTestId(), question.getOrder());
                }
            }
            questions.add(question);
        }

        // Save all questions
        List<Question> savedQuestions = questionRepository.saveAll(questions);

        // Extract metadata from the first question
        Question firstQuestion = savedQuestions.get(0);
        QuestionMetadata metadata = QuestionMetadata.builder()
                .createdAt(firstQuestion.getCreatedAt())
                .updatedAt(firstQuestion.getUpdatedAt())
                .createdBy(firstQuestion.getCreatedBy() != null ? firstQuestion.getCreatedBy().getEmail() : null)
                .updatedBy(firstQuestion.getUpdatedBy() != null ? firstQuestion.getUpdatedBy().getEmail() : null)
                .build();

        List<QuestionDto.QuestionContent> questionContents = questionMapper.toQuestionContentList(savedQuestions);

        return BatchCreateQuestionsResponse.builder()
                .testId(request.getTestId())
                .createdQuestions(savedQuestions.size())
                .metadata(metadata)
                .questions(questionContents)
                .build();
    }

    @Override
    @Transactional
    public BatchDeleteQuestionsResponse batchDeleteQuestions(BatchDeleteQuestionsRequest request) {
        logger.info("Batch deleting questions for test with ID: {}", request.getTestId());

        // Get orders of questions to be deleted
        List<Question> questionsToDelete = questionRepository.findByTestIdAndQuestionIds(
                request.getTestId(), request.getQuestionIds());

        // Question that were actually found
        List<UUID> foundQuestionIds = questionsToDelete.stream()
                .map(Question::getId)
                .toList();

        // IDs that were requested but not found
        List<UUID> notFoundIds = request.getQuestionIds().stream()
                .filter(id -> !foundQuestionIds.contains(id))
                .toList();

        if (questionsToDelete.isEmpty()) {
            logger.warn("No questions found for deletion in test with ID: {}", request.getTestId());
            return BatchDeleteQuestionsResponse.builder()
                    .testId(request.getTestId())
                    .deletedQuestions(0)
                    .failedIds(request.getQuestionIds())
                    .build();
        }

        // Get question orders before deletion
        Map<UUID, Integer> questionOrders = questionsToDelete.stream()
                .collect(Collectors.toMap(Question::getId, Question::getOrder));

        // Delete
        int deletedCount = questionRepository.deleteByTestIdAndQuestionIdIn(
                request.getTestId(), foundQuestionIds);

        logger.info("Deleted {} questions for test with ID: {}", deletedCount, request.getTestId());

        // Reorder remaining questions
        // Just for now, this is not optimal
        questionOrders.forEach((id, order) -> {
            questionRepository.decrementOrdersGreaterThan(request.getTestId(), order);
        });

        return BatchDeleteQuestionsResponse.builder()
                .testId(request.getTestId())
                .deletedQuestions(deletedCount)
                .failedIds(notFoundIds)
                .build();
    }

    @Override
    public BatchUpdateQuestionsResponse batchUpdateQuestions(BatchUpdateQuestionsRequest request) {
        UUID testId = request.getTestId();
        logger.info("Starting batch update for test ID: {}", testId);

        Test test = testRepository.findById(testId)
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + testId));

        List<QuestionUpdateItem> requestItems = request.getQuestions();

        // Handle Deletions
        int deletedCount = handleDeletions(testId, requestItems);

        // Separate items for creation and update (excluding deleted ones)
        List<QuestionUpdateItem> itemsToCreate = requestItems.stream()
                .filter(item -> !item.isDeleted() && item.getId() == null)
                .toList();
        Map<UUID, QuestionUpdateItem> itemsToUpdateMap = requestItems.stream()
                .filter(item -> !item.isDeleted() && item.getId() != null)
                .collect(Collectors.toMap(QuestionUpdateItem::getId, Function.identity()));

        // Handle Creations (new questions)
        Map<Question, QuestionUpdateItem> newQuestionToItemMap = new HashMap<>();
        List<Question> createdQuestions = handleCreations(test, itemsToCreate, newQuestionToItemMap);

        // Handle Updates
        List<Question> updatedQuestions = handleUpdates(testId, itemsToUpdateMap);

        // Combine, Set Order, and Save
        List<Question> questionsToSave = new ArrayList<>(createdQuestions);
        questionsToSave.addAll(updatedQuestions);

        int createCounter = createdQuestions.size();
        int updateCounter = updatedQuestions.size();

        // Apply the order directly from the request
        for (Question question : questionsToSave) {
            QuestionUpdateItem correspondingItem;
            if (question.getId() != null) {
                correspondingItem = itemsToUpdateMap.get(question.getId());
            } else { // Newly created question
                correspondingItem = newQuestionToItemMap.get(question);
            }

            if (correspondingItem != null && correspondingItem.getOrder() != null) {
                question.setOrder(correspondingItem.getOrder());
            } else {
                logger.warn(
                        "Order missing for question (ID: {}, Text: {}) in batch update request. Setting to MAX_VALUE.",
                        question.getId(), question.getText());
                question.setOrder(Integer.MAX_VALUE);
            }
        }

        List<Question> savedQuestions = Collections.emptyList();
        if (!questionsToSave.isEmpty()) {
            savedQuestions = questionRepository.saveAllAndFlush(questionsToSave);
            logger.info("Saved {} questions ({} created, {} updated) for test ID: {}",
                    savedQuestions.size(), createCounter, updateCounter, testId);
        } else {
            logger.info("No questions to create or update for test ID: {}", testId);
        }

        // Construct Response, only the saved (created/updated) questions
        List<QuestionDto.Response> resultingQuestionDtos = savedQuestions.stream()
                .map(questionMapper::toDto)
                .sorted(Comparator.comparingInt(QuestionDto.Response::getOrder)) // Sort the result by order
                .toList();

        return BatchUpdateQuestionsResponse.builder()
                .testId(testId)
                .createdCount(createCounter)
                .updatedCount(updateCounter)
                .deletedCount(deletedCount)
                .resultingQuestions(resultingQuestionDtos)
                .build();
    }

    private int handleDeletions(UUID testId, List<QuestionUpdateItem> requestItems) {
        List<UUID> idsToDelete = requestItems.stream()
                .filter(item -> item.getId() != null && item.isDeleted())
                .map(QuestionUpdateItem::getId)
                .toList();

        if (idsToDelete.isEmpty()) {
            return 0;
        }

        int deletedCount = questionRepository.deleteByTestIdAndQuestionIdIn(testId, idsToDelete);
        logger.info("Deleted {} questions for test ID: {}", deletedCount, testId);
        return deletedCount;
    }

    private List<Question> handleCreations(Test test, List<QuestionUpdateItem> itemsToCreate,
            Map<Question, QuestionUpdateItem> newQuestionToItemMap) {
        if (itemsToCreate.isEmpty()) {
            return Collections.emptyList();
        }

        List<Question> createdQuestions = new ArrayList<>();
        for (QuestionUpdateItem item : itemsToCreate) {
            Question newQuestion = questionMapper.baseToEntity(item);
            newQuestion.setTest(test);
            createdQuestions.add(newQuestion);
            newQuestionToItemMap.put(newQuestion, item);
        }
        logger.info("Prepared {} new questions for creation for test ID: {}", createdQuestions.size(), test.getId());
        return createdQuestions;
    }

    private List<Question> handleUpdates(UUID testId, Map<UUID, QuestionUpdateItem> itemsToUpdateMap) {
        if (itemsToUpdateMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<UUID> questionIdsToUpdate = new ArrayList<>(itemsToUpdateMap.keySet());
        Map<UUID, Question> existingQuestionsMap = questionRepository.findByTestIdAndQuestionIds(testId, questionIdsToUpdate)
                .stream()
                .collect(Collectors.toMap(Question::getId, Function.identity()));

        List<Question> updatedQuestions = new ArrayList<>();
        for (Map.Entry<UUID, QuestionUpdateItem> entry : itemsToUpdateMap.entrySet()) {
            UUID questionId = entry.getKey();
            QuestionUpdateItem item = entry.getValue();
            Question existingQuestion = existingQuestionsMap.get(questionId);

            if (existingQuestion != null) {
                questionMapper.updateEntity(existingQuestion, item);
                // Order is set later
                updatedQuestions.add(existingQuestion);
            } else {
                logger.warn("Question ID {} not found for test ID {}. Skipping.",
                        questionId, testId);
            }
        }
        logger.info("Prepared {} existing questions for update for test ID: {}", updatedQuestions.size(), testId);
        return updatedQuestions;
    }

    @Override
    @Transactional(readOnly = true)
    public Object getTestQuestionsWithFilters(UUID testId, String metric, String difficulty, String sort, String type) {
        // Metric (currenly only "count")
        if (metric != null && !metric.isEmpty()) {
            return switch (metric.toLowerCase()) {
                case "count" -> countQuestionsByTest(testId);
                default -> throw new IllegalArgumentException("Unsupported metric: " + metric);
            };
        }

        // Type filtering
        if (type != null && !type.isEmpty()) {
            return getQuestionsByTestAndType(testId, type);
        }

        // Difficulty filtering
        if (difficulty != null && !difficulty.isEmpty()) {
            return getQuestionsByTestAndDifficulty(testId, difficulty);
        }

        // Ordered sorting
        if ("ordered".equalsIgnoreCase(sort)) {
            return getQuestionsByTestOrdered(testId);
        }

        // Default case: return all questions
        return getQuestionsByTest(testId);
    }
}
