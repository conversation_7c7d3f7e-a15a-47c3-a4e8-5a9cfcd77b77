package io.skillify.services.question;

import java.util.List;
import java.util.UUID;

import io.skillify.dtos.question.QuestionDto;
import io.skillify.dtos.question.QuestionDto.BaseQuestion;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchDeleteQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchDeleteQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.CreateQuestionRequest;

public interface QuestionService {

    QuestionDto.Response createQuestion(CreateQuestionRequest request);

    BatchCreateQuestionsResponse batchCreateQuestions(BatchCreateQuestionsRequest request);

    QuestionDto.Response getQuestion(UUID id);

    List<QuestionDto.Response> getQuestionsByTest(UUID testId);

    List<QuestionDto.Response> getQuestionsByTestOrdered(UUID testId);

    List<QuestionDto.Response> getQuestionsByTestAndType(UUID testId, String type);

    List<QuestionDto.Response> getQuestionsByTestAndDifficulty(UUID testId, String difficulty);

    QuestionDto.Response updateQuestion(UUID id, BaseQuestion request);

    void deleteQuestion(UUID id);

    long countQuestionsByTest(UUID testId);

    BatchDeleteQuestionsResponse batchDeleteQuestions(BatchDeleteQuestionsRequest request);

    BatchUpdateQuestionsResponse batchUpdateQuestions(BatchUpdateQuestionsRequest request);

    Object getTestQuestionsWithFilters(UUID testId, String metric, String difficulty, String sort, String type);
}
