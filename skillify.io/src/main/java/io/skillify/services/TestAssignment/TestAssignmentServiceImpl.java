package io.skillify.services.TestAssignment;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import io.skillify.dtos.test.TestAssignmentDto;
import io.skillify.dtos.test.TestAssignmentDto.CreateTestAssignmentRequest;
import io.skillify.dtos.test.TestAssignmentDto.UpdateTestAssignmentRequest;
import io.skillify.exceptions.ResourceNotFoundException;
import io.skillify.mappers.TestAssignmentMapper;
import io.skillify.models.test.Test;
import io.skillify.models.test.TestAssignment;
import io.skillify.models.test.TestAssignmentStatus;
import io.skillify.models.user.User;
import io.skillify.repositories.test.TestAssignmentRepository;
import io.skillify.repositories.test.TestRepository;
import io.skillify.repositories.user.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TestAssignmentServiceImpl implements TestAssignmentService {

    private final TestAssignmentRepository testAssignmentRepository;
    private final TestRepository testRepository;
    private final UserRepository userRepository;
    private final TestAssignmentMapper testAssignmentMapper;

    @Override
    public List<TestAssignmentDto.Response> createTestAssignments(CreateTestAssignmentRequest request) {
        log.info("Creating test assignments for test ID: {} and {} candidates",
                request.getTestId(), request.getCandidateIds().size());

        Test test = testRepository.findById(request.getTestId())
                .orElseThrow(() -> new ResourceNotFoundException("Test not found with ID: " + request.getTestId()));

        List<TestAssignment> assignments = new ArrayList<>();

        for (UUID candidateId : request.getCandidateIds()) {
            User candidate = userRepository.findById(candidateId)
                    .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + candidateId));

            // Check if assignment already exists
            if (testAssignmentRepository.findByTestIdAndCandidateId(test.getId(), candidateId).isPresent()) {
                log.info("Assignment already exists for test ID: {} and candidate ID: {}", test.getId(), candidateId);
                continue;
            }

            TestAssignment assignment = TestAssignment.builder()
                    .test(test)
                    .candidate(candidate)
                    .status(TestAssignmentStatus.PENDING)
                    .build();

            assignments.add(assignment);
        }

        List<TestAssignment> savedAssignments = testAssignmentRepository.saveAll(assignments);
        return testAssignmentMapper.toDtoList(savedAssignments);
    }

    @Override
    @Transactional(readOnly = true)
    public TestAssignmentDto.Response getTestAssignment(UUID id) {
        log.info("Fetching test assignment with ID: {}", id);

        TestAssignment assignment = testAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Test assignment not found with ID: " + id));

        return testAssignmentMapper.toDto(assignment);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByTest(UUID testId) {
        log.info("Fetching test assignments for test ID: {}", testId);

        List<TestAssignment> assignments = testAssignmentRepository.findByTestId(testId);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByCandidate(UUID candidateId) {
        log.info("Fetching test assignments for candidate ID: {}", candidateId);

        List<TestAssignment> assignments = testAssignmentRepository.findByCandidateId(candidateId);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByStatus(TestAssignmentStatus status) {
        log.info("Fetching test assignments with status: {}", status);

        List<TestAssignment> assignments = testAssignmentRepository.findByStatus(status);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TestAssignmentDto.Response> getTestAssignmentsByTestAndStatus(UUID testId, TestAssignmentStatus status) {
        log.info("Fetching test assignments for test ID: {} with status: {}", testId, status);

        List<TestAssignment> assignments = testAssignmentRepository.findByTestIdAndStatus(testId, status);
        return testAssignmentMapper.toDtoList(assignments);
    }

    @Override
    @Transactional(readOnly = true)
    public TestAssignmentDto.Response getTestAssignmentByTestAndCandidate(UUID testId, UUID candidateId) {
        log.info("Fetching test assignment for test ID: {} and candidate ID: {}", testId, candidateId);

        TestAssignment assignment = testAssignmentRepository.findByTestIdAndCandidateId(testId, candidateId)
                .orElseThrow(() -> new ResourceNotFoundException(
                        "Test assignment not found for test ID: " + testId + " and candidate ID: " + candidateId));

        return testAssignmentMapper.toDto(assignment);
    }

    @Override
    public TestAssignmentDto.Response updateTestAssignment(UUID id, UpdateTestAssignmentRequest request) {
        log.info("Updating test assignment with ID: {} to status: {}", id, request.getStatus());

        TestAssignment assignment = testAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Test assignment not found with ID: " + id));

        testAssignmentMapper.updateEntity(assignment, request);

        // Update timestamps based on status
        if (request.getStatus() == TestAssignmentStatus.IN_PROGRESS && assignment.getStartTime() == null) {
            assignment.setStartTime(OffsetDateTime.now());
        } else if (request.getStatus() == TestAssignmentStatus.COMPLETED && assignment.getEndTime() == null) {
            assignment.setEndTime(OffsetDateTime.now());
        }

        TestAssignment updatedAssignment = testAssignmentRepository.save(assignment);
        return testAssignmentMapper.toDto(updatedAssignment);
    }

    @Override
    public void deleteTestAssignment(UUID id) {
        log.info("Deleting test assignment with ID: {}", id);

        if (!testAssignmentRepository.existsById(id)) {
            throw new ResourceNotFoundException("Test assignment not found with ID: " + id);
        }

        testAssignmentRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTestAssignmentsByTest(UUID testId) {
        log.info("Counting test assignments for test ID: {}", testId);

        return testAssignmentRepository.countByTestId(testId);
    }

    @Override
    @Transactional(readOnly = true)
    public long countTestAssignmentsByTestAndStatus(UUID testId, TestAssignmentStatus status) {
        log.info("Counting test assignments for test ID: {} with status: {}", testId, status);

        return testAssignmentRepository.countByTestIdAndStatus(testId, status);
    }
}
