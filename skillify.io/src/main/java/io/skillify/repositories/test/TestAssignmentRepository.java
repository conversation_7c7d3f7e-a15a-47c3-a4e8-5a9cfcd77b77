package io.skillify.repositories.test;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import io.skillify.models.test.TestAssignment;
import io.skillify.models.test.TestAssignmentStatus;

@Repository
public interface TestAssignmentRepository extends JpaRepository<TestAssignment, UUID> {

    List<TestAssignment> findByTestId(UUID testId);

    List<TestAssignment> findByCandidateId(UUID candidateId);

    List<TestAssignment> findByStatus(TestAssignmentStatus status);

    List<TestAssignment> findByTestIdAndStatus(UUID testId, TestAssignmentStatus status);

    Optional<TestAssignment> findByTestIdAndCandidateId(UUID testId, UUID candidateId);

    long countByTestId(UUID testId);

    long countByTestIdAndStatus(UUID testId, TestAssignmentStatus status);
}
