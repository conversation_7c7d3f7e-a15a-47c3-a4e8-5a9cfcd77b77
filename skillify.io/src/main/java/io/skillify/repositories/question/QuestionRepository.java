package io.skillify.repositories.question;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import io.skillify.models.question.Question;

@Repository
public interface QuestionRepository extends JpaRepository<Question, UUID> {

    List<Question> findByTestId(UUID testId);

    @Query("SELECT q FROM Question q LEFT JOIN FETCH q.test WHERE q.id = :id")
    Optional<Question> findByIdWithTest(@Param("id") UUID id);

    @Query(value = "SELECT test_id FROM questions WHERE question_id = :questionId", nativeQuery = true)
    UUID getTestIdByQuestionId(@Param("questionId") UUID questionId);

    @Query("SELECT q.test.job.organization.id FROM Question q WHERE q.id = :questionId")
    UUID getOrgIdByQuestionId(@Param("questionId") UUID questionId);

    @Query(value = "SELECT \"order\" FROM questions WHERE question_id = :questionId", nativeQuery = true)
    Integer getOrderByQuestionId(@Param("questionId") UUID questionId);

    List<Question> findByTestIdOrderByOrder(UUID testId);

    List<Question> findByTestIdAndType(UUID testId, String type);

    List<Question> findByTestIdAndDifficulty(UUID testId, String difficulty);

    @Query("SELECT MAX(q.order) FROM Question q WHERE q.test.id = :testId")
    Integer findMaxOrderByTestId(UUID testId);

    long countByTestId(UUID testId);

    boolean existsByTestIdAndOrder(UUID testId, Integer order);

    @Modifying
    @Query("UPDATE Question q SET q.order = q.order + 1 WHERE q.test.id = :testId AND q.order >= :orderValue")
    void incrementOrdersGreaterThanOrEqual(@Param("testId") UUID testId, @Param("orderValue") Integer orderValue);

    @Modifying
    @Query(value = "UPDATE questions SET \"order\" = \"order\" - 1 WHERE test_id = :testId AND \"order\" > :orderValue", nativeQuery = true)
    void decrementOrdersGreaterThan(@Param("testId") UUID testId, @Param("orderValue") Integer orderValue);

    @Query("SELECT q FROM Question q WHERE q.test.id = :testId AND q.id IN :questionIds")
    List<Question> findByTestIdAndQuestionIds(@Param("testId") UUID testId, @Param("questionIds") List<UUID> questionIds);

    @Modifying
    @Query("DELETE FROM Question q WHERE q.test.id = :testId AND q.id IN :questionIds")
    int deleteByTestIdAndQuestionIdIn(@Param("testId") UUID testId, @Param("questionIds") List<UUID> questionIds);
}
