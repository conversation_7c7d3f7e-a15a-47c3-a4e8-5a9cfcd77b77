package io.skillify.controllers.test;

import java.util.List;
import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.test.TestDto;
import io.skillify.models.test.Test.TestStatus;
import io.skillify.services.question.QuestionService;
import io.skillify.services.test.TestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/tests")
@RequiredArgsConstructor
@Tag(name = "Test Management", description = "APIs for managing tests")
@SecurityRequirement(name = "JWT")
@Slf4j
public class TestController {

    private final TestService testService;
    private final QuestionService questionService;

    @Operation(
        summary = "Create a new test",
        description = "Creates a new test for a specific job. Requires MANAGE_TEST permission for the job."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "201", description = "Test created successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {TestDto.Response.class}))),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PostMapping
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#createRequest.jobId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<TestDto.Response>> createTest(
            @Parameter(description = "Test creation request", required = true)
            @Valid @RequestBody TestDto.CreateRequest createRequest) {
        TestDto.Response createdTest = testService.createTest(createRequest);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(createdTest));
    }

    @Operation(
        summary = "Update an existing test",
        description = "Updates an existing test by ID. Requires MANAGE_TEST permission."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Test updated successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {TestDto.Response.class}))),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Test or job not found")
    })
    @PatchMapping("/{id}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#id, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<TestDto.Response>> updateTest(
            @Parameter(description = "Test ID", required = true)
            @PathVariable UUID id,
            @Parameter(description = "Test update request", required = true)
            @Valid @RequestBody TestDto.UpdateRequest updateRequest) {
        TestDto.Response updatedTest = testService.updateTest(id, updateRequest);
        return ResponseEntity.ok(ResponseDto.success(updatedTest));
    }

    @Operation(
        summary = "Get test by ID",
        description = "Retrieves a test by its ID. Requires MANAGE_TEST permission for the test."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Test retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {TestDto.Response.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Test not found")
    })
    @GetMapping("/{id}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#id, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<TestDto.Response>> getTestById(
            @Parameter(description = "Test ID", required = true)
            @PathVariable UUID id) {
        TestDto.Response test = testService.getTestById(id);
        return ResponseEntity.ok(ResponseDto.success(test));
    }

    @Operation(
        summary = "Get all tests",
        description = "Retrieves all tests. Requires ROLE_ADMIN."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Tests retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {List.class, TestDto.Response.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    @GetMapping
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<ResponseDto<List<TestDto.Response>>> getAllTests() {
        List<TestDto.Response> tests = testService.getAllTests();
        return ResponseEntity.ok(ResponseDto.success(tests));
    }

    @Operation(
        summary = "Get tests by job ID",
        description = "Retrieves all tests for a specific job. Requires MANAGE_TEST permission for the job."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Tests retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {List.class, TestDto.Response.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @GetMapping("/job/{jobId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<List<TestDto.Response>>> getTestsByJobId(
            @Parameter(description = "Job ID", required = true)
            @PathVariable UUID jobId) {
        List<TestDto.Response> tests = testService.getTestsByJobId(jobId);
        return ResponseEntity.ok(ResponseDto.success(tests));
    }

    @Operation(
        summary = "Get tests by status",
        description = "Retrieves all tests with a specific status. Requires ROLE_ADMIN."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Tests retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {List.class, TestDto.Response.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    public ResponseEntity<ResponseDto<List<TestDto.Response>>> getTestsByStatus(
            @Parameter(description = "Test status", required = true)
            @PathVariable TestStatus status) {
        List<TestDto.Response> tests = testService.getTestsByStatus(status);
        return ResponseEntity.ok(ResponseDto.success(tests));
    }

    @Operation(
        summary = "Get tests by job ID and status",
        description = "Retrieves all tests for a specific job with a specific status. Requires MANAGE_TEST permission for the job."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Tests retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {List.class, TestDto.Response.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @GetMapping("/job/{jobId}/status")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<List<TestDto.Response>>> getTestsByJobIdAndStatus(
            @Parameter(description = "Job ID", required = true)
            @PathVariable UUID jobId,
            @Parameter(description = "Test status", required = true)
            @RequestParam TestStatus status) {
        List<TestDto.Response> tests = testService.getTestsByJobIdAndStatus(jobId, status);
        return ResponseEntity.ok(ResponseDto.success(tests));
    }

    @Operation(
        summary = "Delete a test",
        description = "Deletes a test by its ID. Requires MANAGE_TEST permission for the test."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Test deleted successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {Void.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Test not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#id, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<Void>> deleteTest(
            @Parameter(description = "Test ID", required = true)
            @PathVariable UUID id) {
        testService.deleteTest(id);
        return ResponseEntity.ok(ResponseDto.success(null));
    }

    @Operation(
        summary = "Count tests by job ID",
        description = "Counts the number of tests for a specific job. Requires MANAGE_TEST permission for the job."
    )
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "Count retrieved successfully",
                    content = @Content(schema = @Schema(implementation = ResponseDto.class, subTypes = {Long.class}))),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @GetMapping("/job/{jobId}/count")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_TEST')")
    public ResponseEntity<ResponseDto<Long>> countTestsByJobId(
            @Parameter(description = "Job ID", required = true)
            @PathVariable UUID jobId) {
        long count = testService.countTestsByJobId(jobId);
        return ResponseEntity.ok(ResponseDto.success(count));
    }

    @GetMapping("/{testId}/questions")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get test questions with filtering and sorting options", description = "Retrieves questions for a test with support for filtering and sorting via query parameters")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Questions or metrics retrieved successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid parameters"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Test not found")
    })
    public ResponseEntity<ResponseDto<?>> getQuestionsByTest(
            @Parameter(description = "Test ID", required = true) @PathVariable UUID testId,
            @Parameter(description = "Optional metric (count)", schema = @Schema(allowableValues = {
                    "count" })) @RequestParam(required = false) String metric,
            @Parameter(description = "Optional difficulty filter", schema = @Schema(allowableValues = { "EASY",
                    "MEDIUM", "HARD" })) @RequestParam(required = false) String difficulty,
            @Parameter(description = "Optional sort by order", schema = @Schema(allowableValues = {
                    "ordered" })) @RequestParam(required = false) String sort,
            @Parameter(description = "Optional filter by question type") @RequestParam(required = false) String type) {

        log.info("Fetching questions for test {} with parameters - metric: {}, difficulty: {}, sort: {}, type: {}",
                testId, metric, difficulty, sort, type);

        Object result = questionService.getTestQuestionsWithFilters(testId, metric, difficulty, sort, type);
        return ResponseEntity.ok(ResponseDto.success(result));
    }

    @GetMapping("/{testId}/results")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get test results", description = "Retrieves the results of a test, including all candidates and their answers")
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Test results retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Test not found")
    })
    public ResponseEntity<ResponseDto<AnswerDto.TestAnswersResponse>> getTestResults(
            @Parameter(description = "Test ID", required = true) @PathVariable UUID testId,
            @Parameter(description = "Optional filter by candidate ID") @RequestParam(required = false) UUID candidateId,
            @Parameter(description = "Optional filter by question ID") @RequestParam(required = false) UUID questionId) {

        log.info("Fetching results for test {} with parameters - candidateId: {}, questionId: {}",
                 testId, candidateId, questionId);

        AnswerDto.TestAnswersResponse result = testService.getTestResults(testId, candidateId, questionId);
        return ResponseEntity.ok(ResponseDto.success(result));
    }
}
