package io.skillify.controllers.test;

import java.util.List;
import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.test.TestAssignmentDto;
import io.skillify.dtos.test.TestAssignmentDto.CreateTestAssignmentRequest;
import io.skillify.dtos.test.TestAssignmentDto.UpdateTestAssignmentRequest;
import io.skillify.models.test.TestAssignmentStatus;
import io.skillify.services.TestAssignment.TestAssignmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/test-assignments")
@RequiredArgsConstructor
@Tag(name = "Test Assignments", description = "API for managing test assignments")
public class TestAssignmentController {

    private final TestAssignmentService testAssignmentService;

    @PostMapping
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#request.testId, 'MANAGE_TEST')")
    @Operation(summary = "Create test assignments", description = "Creates test assignments for multiple candidates")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> createTestAssignments(
            @Valid @RequestBody CreateTestAssignmentRequest request) {
        List<TestAssignmentDto.Response> createdAssignments = testAssignmentService.createTestAssignments(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(createdAssignments));
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionForTestAssignment(#id, 'MANAGE_TEST')")
    @Operation(summary = "Get a test assignment by ID", description = "Retrieves a test assignment by its ID")
    public ResponseEntity<ResponseDto<TestAssignmentDto.Response>> getTestAssignment(@PathVariable UUID id) {
        TestAssignmentDto.Response assignment = testAssignmentService.getTestAssignment(id);
        return ResponseEntity.ok(ResponseDto.success(assignment));
    }

    @GetMapping("/test/{testId}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get test assignments by test", description = "Retrieves all test assignments for a specific test")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> getTestAssignmentsByTest(@PathVariable UUID testId) {
        List<TestAssignmentDto.Response> assignments = testAssignmentService.getTestAssignmentsByTest(testId);
        return ResponseEntity.ok(ResponseDto.success(assignments));
    }

    @GetMapping("/candidate/{candidateId}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or #candidateId == authentication.principal.id")
    @Operation(summary = "Get test assignments by candidate", description = "Retrieves all test assignments for a specific candidate")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> getTestAssignmentsByCandidate(@PathVariable UUID candidateId) {
        List<TestAssignmentDto.Response> assignments = testAssignmentService.getTestAssignmentsByCandidate(candidateId);
        return ResponseEntity.ok(ResponseDto.success(assignments));
    }

    @GetMapping("/status")
    @PreAuthorize("hasRole('ROLE_ADMIN')")
    @Operation(summary = "Get test assignments by status", description = "Retrieves all test assignments with a specific status")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> getTestAssignmentsByStatus(
            @RequestParam TestAssignmentStatus status) {
        List<TestAssignmentDto.Response> assignments = testAssignmentService.getTestAssignmentsByStatus(status);
        return ResponseEntity.ok(ResponseDto.success(assignments));
    }

    @GetMapping("/test/{testId}/status")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionforTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get test assignments by test and status", description = "Retrieves all test assignments for a specific test with a specific status")
    public ResponseEntity<ResponseDto<List<TestAssignmentDto.Response>>> getTestAssignmentsByTestAndStatus(
            @PathVariable UUID testId,
            @RequestParam TestAssignmentStatus status) {
        List<TestAssignmentDto.Response> assignments = testAssignmentService.getTestAssignmentsByTestAndStatus(testId, status);
        return ResponseEntity.ok(ResponseDto.success(assignments));
    }

    @GetMapping("/test/{testId}/candidate/{candidateId}")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get test assignment by test and candidate", description = "Retrieves a test assignment for a specific test and candidate")
    public ResponseEntity<ResponseDto<TestAssignmentDto.Response>> getTestAssignmentByTestAndCandidate(
            @PathVariable UUID testId,
            @PathVariable UUID candidateId) {
        TestAssignmentDto.Response assignment = testAssignmentService.getTestAssignmentByTestAndCandidate(testId, candidateId);
        return ResponseEntity.ok(ResponseDto.success(assignment));
    }

    @PatchMapping("/{id}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTestAssignment(#id, 'MANAGE_TEST')")
    @Operation(summary = "Update a test assignment", description = "Updates an existing test assignment")
    public ResponseEntity<ResponseDto<TestAssignmentDto.Response>> updateTestAssignment(
            @PathVariable UUID id,
            @Valid @RequestBody UpdateTestAssignmentRequest request) {
        TestAssignmentDto.Response updatedAssignment = testAssignmentService.updateTestAssignment(id, request);
        return ResponseEntity.ok(ResponseDto.success(updatedAssignment));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTestAssignment(#id, 'MANAGE_TEST')")
    @Operation(summary = "Delete a test assignment", description = "Deletes a test assignment by its ID")
    public ResponseEntity<ResponseDto<Void>> deleteTestAssignment(@PathVariable UUID id) {
        testAssignmentService.deleteTestAssignment(id);
        return ResponseEntity.ok(ResponseDto.success(null));
    }

    @GetMapping("/test/{testId}/count")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionforTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Count test assignments by test", description = "Counts the number of test assignments for a specific test")
    public ResponseEntity<ResponseDto<Long>> countTestAssignmentsByTest(@PathVariable UUID testId) {
        long count = testAssignmentService.countTestAssignmentsByTest(testId);
        return ResponseEntity.ok(ResponseDto.success(count));
    }

    @GetMapping("/test/{testId}/status/count")
    @PreAuthorize("hasRole('ROLE_ADMIN') or @orgPermissionEvaluator.hasPermissionforTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Count test assignments by test and status", description = "Counts the number of test assignments for a specific test with a specific status")
    public ResponseEntity<ResponseDto<Long>> countTestAssignmentsByTestAndStatus(
            @PathVariable UUID testId,
            @RequestParam TestAssignmentStatus status) {
        long count = testAssignmentService.countTestAssignmentsByTestAndStatus(testId, status);
        return ResponseEntity.ok(ResponseDto.success(count));
    }
}
