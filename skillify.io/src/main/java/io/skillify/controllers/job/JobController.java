package io.skillify.controllers.job;

import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.job.JobDto;
import io.skillify.services.job.JobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/jobs")
@RequiredArgsConstructor
@Validated
@Tag(name = "Jobs", description = "Job management APIs")
public class JobController {

    private final JobService jobService;

    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Create a new job posting", description = "Creates a new job posting for an organization. Requires MANAGE_JOB permission.", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses({
            @ApiResponse(responseCode = "201", description = "Job created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#request.organizationId, 'MANAGE_JOB')")
    public ResponseEntity<ResponseDto<JobDto.Response>> createJob(
            @Validated @RequestBody JobDto.CreateRequest request,
            Authentication authentication) {
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(jobService.createJob(request, authentication)));
    }

    @PatchMapping("/{jobId}")
    @Operation(summary = "Update an existing job", description = "Updates an existing job posting. Requires MANAGE_JOB permission.", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Job updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_JOB')")
    public ResponseEntity<ResponseDto<JobDto.Response>> updateJob(
            @Parameter(description = "Job ID", required = true) @PathVariable UUID jobId,
            @Validated @RequestBody JobDto.UpdateRequest request,
            Authentication authentication) {
        return ResponseEntity.ok(ResponseDto.success(jobService.updateJob(jobId, request, authentication)));
    }

    @GetMapping("/{jobId}")
    @Operation(summary = "Get job details", description = "Retrieves details of a specific job posting", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Job details retrieved successfully", content = @Content(schema = @Schema(implementation = JobDto.Response.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_JOB')")
    public ResponseEntity<?> getJob(
            @Parameter(description = "ID of the job to retrieve", required = true) @PathVariable UUID jobId) {
        JobDto.Response response = jobService.getJob(jobId);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @GetMapping("/org/{organizationId}")
    @Operation(summary = "Get organization jobs", description = "Retrieves all jobs for a specific organization", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Organization jobs retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden"),
            @ApiResponse(responseCode = "404", description = "Organization not found")
    })
    @PreAuthorize("@orgPermissionEvaluator.hasPermission(#organizationId, 'MANAGE_JOB')")
    public ResponseEntity<?> getOrganizationJobs(
            @Parameter(description = "ID of the organization to fetch jobs for", required = true) @PathVariable UUID organizationId) {
        return ResponseEntity.ok(ResponseDto.success(jobService.getOrganizationJobs(organizationId)));
    }

    @DeleteMapping("/{jobId}")
    @Operation(summary = "Delete a job", description = "Deletes an existing job posting. Requires MANAGE_JOB permission.", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses({
            @ApiResponse(responseCode = "200", description = "Job deleted successfully"),
            @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
            @ApiResponse(responseCode = "404", description = "Job not found")
    })
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForJob(#jobId, 'MANAGE_JOB')")
    public ResponseEntity<?> deleteJob(
            @Parameter(description = "ID of the job to delete", required = true) @PathVariable UUID jobId) {
        jobService.deleteJob(jobId);
        return ResponseEntity.ok(ResponseDto.success("Job deleted successfully"));
    }
}
