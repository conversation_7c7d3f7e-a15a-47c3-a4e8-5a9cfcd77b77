package io.skillify.controllers.auth;

import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.auth.CheckCredentialsRequest;
import io.skillify.dtos.auth.ContinueWithOAuth;
import io.skillify.dtos.auth.LoginRequest;
import io.skillify.dtos.auth.RegisterRequest;
import io.skillify.models.user.User;
import io.skillify.services.auth.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@Validated
@RequestMapping("/api/auth")
@Tag(name = "Authentication", description = "Authentication API endpoints")
public class AuthController {

    private final AuthService authService;

    @PostMapping(value = "/login", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "Login user", description = "Authenticates a user and sets JWT in an HttpOnly cookie")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully authenticated"),
            @ApiResponse(responseCode = "401", description = "Invalid credentials"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequest request) {
        return ResponseEntity.ok(authService.login(request.getEmail(), request.getPassword()));
    }

    @PostMapping(value = "/register", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.CREATED)
    @Operation(summary = "Register user", description = "Creates a new user account")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "User successfully registered"),
            @ApiResponse(responseCode = "400", description = "Invalid input"),
            @ApiResponse(responseCode = "409", description = "User already exists")
    })
    public ResponseEntity<?> register(@Valid @RequestBody RegisterRequest request) {
        User user = User.builder()
                .email(request.getEmail())
                .username(request.getUsername())
                .password(request.getPassword())
                .build();

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(authService.register(user, "ROLE_ADMIN")));
    }

    @PostMapping(value = "/oauth/{provider}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "OAuth authentication", description = "Authenticate or register via OAuth provider")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully authenticated"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<?> oauth(
            @PathVariable String provider,
            @Valid @RequestBody ContinueWithOAuth request) {
        return ResponseEntity.ok(authService.oauth(request.getEmail(), "ROLE_ADMIN", request.getName(), request.getOauthId()));
    }

    @PostMapping("/token/refresh")
    @Operation(summary = "Refresh token", description = "Refreshes JWT token using a valid refresh token cookie")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Token refreshed successfully"),
            @ApiResponse(responseCode = "401", description = "Invalid or expired refresh token")
    })
    public ResponseEntity<?> refreshToken(HttpServletRequest request) {
        String message = authService.refreshToken(request);
        return ResponseEntity.ok(ResponseDto.success(message));
    }

    @PostMapping("/logout")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "Logout user", description = "Logs out the user and clears JWT cookies", security = @SecurityRequirement(name = "bearerAuth"))
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Successfully logged out"),
            @ApiResponse(responseCode = "401", description = "Not authenticated")
    })
    public ResponseEntity<?> logout(HttpServletRequest request) {
        authService.logout(request);
        return ResponseEntity.ok(ResponseDto.success("Logged out successfully"));
    }

    @PostMapping("/credentials/check")
    @PreAuthorize("permitAll()")
    @Operation(summary = "Check if credentials exist", description = "Checks if email or username already exists")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Credentials checked successfully", content = @Content(schema = @Schema(implementation = ResponseDto.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })

    public ResponseEntity<?> checkCredentials(@Valid @RequestBody CheckCredentialsRequest request) {
        return ResponseEntity.ok(authService.checkCredentials(request));
    }
}
