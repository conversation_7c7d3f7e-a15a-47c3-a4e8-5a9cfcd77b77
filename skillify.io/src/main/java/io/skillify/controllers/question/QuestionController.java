package io.skillify.controllers.question;

import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.question.QuestionDto;
import io.skillify.dtos.question.QuestionDto.BaseQuestion;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchCreateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsRequest;
import io.skillify.dtos.question.QuestionDto.BatchUpdateQuestionsResponse;
import io.skillify.dtos.question.QuestionDto.CreateQuestionRequest;
import io.skillify.services.question.QuestionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/questions")
@RequiredArgsConstructor
@Tag(name = "Questions", description = "API for managing questions")
public class QuestionController {

    private final QuestionService questionService;
    Logger logger = LoggerFactory.getLogger(QuestionController.class);

    @PostMapping
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#request.testId, 'MANAGE_TEST')")
    @Operation(summary = "Create a new question", description = "Creates a new question for a test")
    public ResponseEntity<ResponseDto<QuestionDto.Response>> createQuestion(
            @Valid @RequestBody CreateQuestionRequest request) {
        QuestionDto.Response createdQuestion = questionService.createQuestion(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(createdQuestion));
    }

    @PostMapping("/batch")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#request.testId, 'MANAGE_TEST')")
    @Operation(
        summary = "Create multiple questions in one operation",
        description = "Creates multiple questions for a test in a single request"
    )
    public ResponseEntity<ResponseDto<BatchCreateQuestionsResponse>> batchCreateQuestions(
            @Valid @RequestBody BatchCreateQuestionsRequest request) {
        logger.info("Received batch create request for {} questions for test {}",
                request.getQuestions().size(), request.getTestId());

        BatchCreateQuestionsResponse response = questionService.batchCreateQuestions(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(response));
    }

    @DeleteMapping("/batch")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#request.testId, 'MANAGE_TEST')")
    @Operation(summary = "Delete multiple questions")
    public ResponseEntity<ResponseDto<?>> batchDeleteQuestions(
            @Valid @RequestBody QuestionDto.BatchDeleteQuestionsRequest request) {
        logger.info("Received batch delete request for {} questions from test {}",
                request.getQuestionIds().size(), request.getTestId());

        QuestionDto.BatchDeleteQuestionsResponse response = questionService.batchDeleteQuestions(request);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @PatchMapping("/batch")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#request.testId, 'MANAGE_TEST')")
    @Operation(
        summary = "Update multiple questions in one operation",
        description = "Updates, creates, or deletes multiple questions for a test based on the provided list."
    )
    public ResponseEntity<ResponseDto<BatchUpdateQuestionsResponse>> batchUpdateQuestions(
            @Valid @RequestBody BatchUpdateQuestionsRequest request) {
        logger.info("Received batch update request for {} questions for test {}",
                request.getQuestions().size(), request.getTestId());

        BatchUpdateQuestionsResponse response = questionService.batchUpdateQuestions(request);
        return ResponseEntity.ok(ResponseDto.success(response));
    }

    @GetMapping("/{questionId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForQuestion(#questionId, 'MANAGE_TEST')")
    @Operation(summary = "Get a question by ID", description = "Retrieves a question by its ID")
    public ResponseEntity<ResponseDto<QuestionDto.Response>> getQuestion(@PathVariable UUID questionId) {
        QuestionDto.Response question = questionService.getQuestion(questionId);
        return ResponseEntity.ok(ResponseDto.success(question));
    }

    @PatchMapping("/{questionId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForQuestion(#questionId, 'MANAGE_TEST')")
    @Operation(summary = "Update a question", description = "Updates an existing question")
    public ResponseEntity<ResponseDto<QuestionDto.Response>> updateQuestion(
            @PathVariable UUID questionId,
            @Valid @RequestBody BaseQuestion request) {
        QuestionDto.Response updatedQuestion = questionService.updateQuestion(questionId, request);
        return ResponseEntity.ok(ResponseDto.success(updatedQuestion));
    }

    @DeleteMapping("/{questionId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForQuestion(#questionId, 'MANAGE_TEST')")
    @Operation(summary = "Delete a question", description = "Deletes a question by its ID")
    public ResponseEntity<ResponseDto<Void>> deleteQuestion(@PathVariable UUID questionId) {
        questionService.deleteQuestion(questionId);
        return ResponseEntity.ok(ResponseDto.success(null));
    }
}
