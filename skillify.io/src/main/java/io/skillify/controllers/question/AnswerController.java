package io.skillify.controllers.question;

import java.util.List;
import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import io.skillify.dtos.ResponseDto;
import io.skillify.dtos.question.AnswerDto;
import io.skillify.dtos.question.AnswerDto.BatchSubmissionResponse;
import io.skillify.dtos.question.AnswerDto.BatchSubmitAnswerRequest;
import io.skillify.dtos.question.AnswerDto.SubmitAnswerRequest;
import io.skillify.models.question.Answer.AnswerId;
import io.skillify.services.question.AnswerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/answers")
@RequiredArgsConstructor
@Tag(name = "Answers", description = "API for managing answers")
public class AnswerController {

    private final AnswerService answerService;

    @PostMapping
    @PreAuthorize("@orgPermissionEvaluator.canTakeTest(#request.testId)")
    @Operation(summary = "Submit an answer", description = "Submits an answer for a question")
    public ResponseEntity<ResponseDto<AnswerDto.Response>> submitAnswer(
            @Valid @RequestBody SubmitAnswerRequest request) {
        AnswerDto.Response submittedAnswer = answerService.submitAnswer(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(submittedAnswer));
    }

    @PostMapping("/batch")
    @PreAuthorize("@orgPermissionEvaluator.canTakeTest(#request.testId)")
    @Operation(summary = "Submit multiple answers",
              description = "Submits multiple answers for a test")
    public ResponseEntity<ResponseDto<BatchSubmissionResponse>> submitBatchAnswers(
            @Valid @RequestBody BatchSubmitAnswerRequest request) {
        BatchSubmissionResponse response = answerService.submitBatchAnswers(request);
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ResponseDto.success(response));
    }

    @GetMapping("/test/{testId}/question/{questionId}/candidate/{candidateId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get an answer by test, question, and candidate", description = "Retrieves an answer by its composite key")
    public ResponseEntity<ResponseDto<AnswerDto.Response>> getAnswer(
            @PathVariable UUID testId,
            @PathVariable UUID questionId,
            @PathVariable UUID candidateId) {
        AnswerDto.Response answer = answerService.getAnswer(new AnswerId(testId, questionId, candidateId));
        return ResponseEntity.ok(ResponseDto.success(answer));
    }

    @GetMapping("/test/{testId}/candidate/{candidateId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Get answers by test and candidate", description = "Retrieves all answers for a specific test and candidate")
    public ResponseEntity<ResponseDto<List<AnswerDto.Response>>> getAnswersByTestAndCandidate(
            @PathVariable UUID testId,
            @PathVariable UUID candidateId) {
        List<AnswerDto.Response> answers = answerService.getAnswersByTestAndCandidate(testId, candidateId);
        return ResponseEntity.ok(ResponseDto.success(answers));
    }

    @GetMapping("/question/{questionId}/candidate/{candidateId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForQuestion(#questionId, 'MANAGE_TEST')")
    @Operation(summary = "Get answers by question and candidate", description = "Retrieves all answers for a specific question and candidate")
    public ResponseEntity<ResponseDto<List<AnswerDto.Response>>> getAnswersByQuestionAndCandidate(
            @PathVariable UUID questionId,
            @PathVariable UUID candidateId) {
        List<AnswerDto.Response> answers = answerService.getAnswersByQuestionAndCandidate(questionId, candidateId);
        return ResponseEntity.ok(ResponseDto.success(answers));
    }

    @GetMapping("/test/{testId}/candidate/{candidateId}/average-score")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Calculate average score", description = "Calculates the average score for a specific test and candidate")
    public ResponseEntity<ResponseDto<Float>> calculateAverageScoreByTestAndCandidate(
            @PathVariable UUID testId,
            @PathVariable UUID candidateId) {
        Float averageScore = answerService.calculateAverageScoreByTestAndCandidate(testId, candidateId);
        return ResponseEntity.ok(ResponseDto.success(averageScore));
    }

    @GetMapping("/test/{testId}/candidate/{candidateId}/correct-count")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Count correct answers", description = "Counts the number of correct answers for a specific test and candidate")
    public ResponseEntity<ResponseDto<Long>> countCorrectAnswersByTestAndCandidate(
            @PathVariable UUID testId,
            @PathVariable UUID candidateId) {
        Long count = answerService.countCorrectAnswersByTestAndCandidate(testId, candidateId);
        return ResponseEntity.ok(ResponseDto.success(count));
    }

    @GetMapping("/test/{testId}/candidate/{candidateId}/total-count")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Count total answers", description = "Counts the total number of answers for a specific test and candidate")
    public ResponseEntity<ResponseDto<Long>> countTotalAnswersByTestAndCandidate(
            @PathVariable UUID testId,
            @PathVariable UUID candidateId) {
        Long count = answerService.countTotalAnswersByTestAndCandidate(testId, candidateId);
        return ResponseEntity.ok(ResponseDto.success(count));
    }

    @DeleteMapping("/test/{testId}/question/{questionId}/candidate/{candidateId}")
    @PreAuthorize("@orgPermissionEvaluator.hasPermissionForTest(#testId, 'MANAGE_TEST')")
    @Operation(summary = "Delete an answer", description = "Deletes an answer by its composite key")
    public ResponseEntity<ResponseDto<Void>> deleteAnswer(
            @PathVariable UUID testId,
            @PathVariable UUID questionId,
            @PathVariable UUID candidateId) {
        answerService.deleteAnswer(new AnswerId(testId, questionId, candidateId));
        return ResponseEntity.ok(ResponseDto.success(null));
    }
}
