import json
import logging
import traceback
from datetime import datetime
from flask import jsonify, request
from threading import Lock
import time

# Thread-safe lock for log access
logs_lock = Lock()

@app.route("/api/analysis/jobs/<job_id>/logs", methods=["GET"])
def get_analysis_job_logs(job_id):
    """Get logs for a specific analysis job with robust error handling."""
    try:
        # Validate job_id
        if not job_id or not job_id.strip():
            return jsonify({"error": "Invalid job ID"}), 400

        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 100, type=int)
        since = request.args.get('since', type=str)  # ISO timestamp for incremental updates
        
        # Limit the maximum number of logs per request
        limit = min(limit, 1000)
        
        # Thread-safe log retrieval
        with logs_lock:
            try:
                logs = background_worker.get_job_logs(job_id)
            except Exception as worker_error:
                logging.error(f"Background worker error for job {job_id}: {str(worker_error)}")
                return jsonify({"error": "Failed to retrieve logs from worker", "details": str(worker_error)}), 500

        # Handle empty logs
        if not logs:
            return jsonify({
                "logs": [],
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": 0,
                    "has_more": False
                },
                "timestamp": datetime.utcnow().isoformat()
            })

        # Filter logs by timestamp if 'since' parameter is provided
        if since:
            try:
                since_dt = datetime.fromisoformat(since.replace('Z', '+00:00'))
                logs = [log for log in logs if datetime.fromisoformat(log.get('timestamp', '1970-01-01').replace('Z', '+00:00')) > since_dt]
            except (ValueError, TypeError) as e:
                logging.warning(f"Invalid 'since' timestamp format: {since}, error: {str(e)}")

        # Robust data serialization
        serialized_logs = []
        for i, log in enumerate(logs):
            try:
                serialized_log = serialize_log_entry(log, job_id, i)
                if serialized_log:
                    serialized_logs.append(serialized_log)
            except Exception as serialize_error:
                logging.error(f"Failed to serialize log entry {i} for job {job_id}: {str(serialize_error)}")
                # Add a fallback log entry
                serialized_logs.append({
                    "_id": f"{job_id}_error_{i}",
                    "message": f"Log entry {i} could not be serialized",
                    "timestamp": datetime.utcnow().isoformat(),
                    "level": "error",
                    "job_id": job_id
                })

        # Apply pagination
        total_logs = len(serialized_logs)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_logs = serialized_logs[start_idx:end_idx]

        # Prepare response
        response_data = {
            "logs": paginated_logs,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_logs,
                "has_more": end_idx < total_logs
            },
            "timestamp": datetime.utcnow().isoformat(),
            "job_id": job_id
        }

        return jsonify(response_data), 200

    except Exception as e:
        # Comprehensive error logging
        error_details = {
            "error": "Internal server error in get_analysis_job_logs",
            "job_id": job_id,
            "exception_type": type(e).__name__,
            "exception_message": str(e),
            "traceback": traceback.format_exc(),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        logging.error(f"Critical error in get_analysis_job_logs: {json.dumps(error_details, indent=2)}")
        
        # Return user-friendly error
        return jsonify({
            "error": "Failed to retrieve logs",
            "message": "An internal error occurred while fetching logs",
            "job_id": job_id,
            "timestamp": datetime.utcnow().isoformat()
        }), 500


def serialize_log_entry(log, job_id, index):
    """Safely serialize a log entry with comprehensive error handling."""
    try:
        # Create a clean copy of the log
        serialized = {}
        
        # Handle different log formats
        if isinstance(log, dict):
            base_log = log.copy()
        elif hasattr(log, '__dict__'):
            base_log = log.__dict__.copy()
        else:
            # Fallback for unknown log types
            base_log = {"message": str(log)}

        # Essential fields with defaults
        serialized["_id"] = base_log.get("_id") or base_log.get("id") or f"{job_id}_{index}_{int(time.time())}"
        serialized["message"] = str(base_log.get("message", "No message"))
        serialized["timestamp"] = base_log.get("timestamp") or datetime.utcnow().isoformat()
        serialized["job_id"] = job_id
        
        # Optional fields with safe extraction
        optional_fields = ["level", "source", "component", "step", "progress", "details", "metadata"]
        for field in optional_fields:
            if field in base_log:
                try:
                    value = base_log[field]
                    if value is not None:
                        serialized[field] = safe_serialize_value(value)
                except Exception as field_error:
                    logging.warning(f"Failed to serialize field '{field}' in log {index}: {str(field_error)}")

        # Ensure timestamp is properly formatted
        try:
            if serialized["timestamp"] and not serialized["timestamp"].endswith('Z'):
                # Try to parse and reformat timestamp
                dt = datetime.fromisoformat(serialized["timestamp"].replace('Z', '+00:00'))
                serialized["timestamp"] = dt.isoformat()
        except Exception as ts_error:
            logging.warning(f"Timestamp formatting issue for log {index}: {str(ts_error)}")
            serialized["timestamp"] = datetime.utcnow().isoformat()

        return serialized

    except Exception as e:
        logging.error(f"Critical error serializing log entry {index}: {str(e)}")
        # Return a minimal safe log entry
        return {
            "_id": f"{job_id}_fallback_{index}_{int(time.time())}",
            "message": f"Log entry could not be processed: {str(e)[:100]}",
            "timestamp": datetime.utcnow().isoformat(),
            "level": "error",
            "job_id": job_id
        }


def safe_serialize_value(value):
    """Safely serialize any value to JSON-compatible format."""
    try:
        # Test if value is already JSON serializable
        json.dumps(value)
        return value
    except (TypeError, ValueError):
        # Handle non-serializable objects
        if hasattr(value, '__dict__'):
            return {k: safe_serialize_value(v) for k, v in value.__dict__.items()}
        elif hasattr(value, '__iter__') and not isinstance(value, (str, bytes)):
            return [safe_serialize_value(item) for item in value]
        else:
            return str(value)


# Additional endpoint for real-time streaming (Server-Sent Events)
@app.route("/api/analysis/jobs/<job_id>/logs/stream", methods=["GET"])
def stream_analysis_job_logs(job_id):
    """Stream logs in real-time using Server-Sent Events."""
    def generate_log_stream():
        last_log_count = 0
        last_check = time.time()
        
        while True:
            try:
                with logs_lock:
                    logs = background_worker.get_job_logs(job_id)
                
                if logs and len(logs) > last_log_count:
                    # Send new logs
                    new_logs = logs[last_log_count:]
                    for log in new_logs:
                        try:
                            serialized = serialize_log_entry(log, job_id, last_log_count)
                            yield f"data: {json.dumps(serialized)}\n\n"
                            last_log_count += 1
                        except Exception as e:
                            logging.error(f"Error streaming log: {str(e)}")
                
                # Check if job is complete
                try:
                    job_status = background_worker.get_job_status(job_id)
                    if job_status and job_status.get('status') in ['completed', 'failed']:
                        yield f"event: job_complete\ndata: {json.dumps({'status': job_status.get('status')})}\n\n"
                        break
                except Exception as status_error:
                    logging.warning(f"Could not check job status: {str(status_error)}")
                
                time.sleep(1)  # Poll every second
                
                # Timeout after 10 minutes
                if time.time() - last_check > 600:
                    yield f"event: timeout\ndata: {json.dumps({'message': 'Stream timeout'})}\n\n"
                    break
                    
            except Exception as e:
                logging.error(f"Error in log stream: {str(e)}")
                yield f"event: error\ndata: {json.dumps({'error': str(e)})}\n\n"
                break

    return app.response_class(
        generate_log_stream(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        }
    )


# Health check endpoint for the logs system
@app.route("/api/analysis/jobs/<job_id>/logs/health", methods=["GET"])
def check_logs_health(job_id):
    """Check if the logs system is working properly for a job."""
    try:
        with logs_lock:
            logs = background_worker.get_job_logs(job_id)
        
        return jsonify({
            "status": "healthy",
            "job_id": job_id,
            "log_count": len(logs) if logs else 0,
            "timestamp": datetime.utcnow().isoformat()
        }), 200
    except Exception as e:
        return jsonify({
            "status": "unhealthy",
            "job_id": job_id,
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }), 500
